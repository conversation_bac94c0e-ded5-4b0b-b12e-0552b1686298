<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目详情 - DevOps 平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
            color: #4a5568; /* Default text color */
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .logo {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .logo h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .logo p {
            font-size: 12px;
            opacity: 0.8;
        }

        .nav-section {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .nav-title {
            padding: 20px;
            font-size: 14px;
            font-weight: 600;
            opacity: 0.9;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-items {
            padding: 10px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            color: white;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: rgba(255,255,255,0.15);
            transition: width 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .nav-item:hover::before {
            width: 4px;
        }

        .nav-item.active {
            background: rgba(255,255,255,0.15);
            transform: translateX(5px);
        }

        .nav-item.active::before {
            width: 4px;
            background: #ffd700;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.9;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
        }

        .header {
            background: white;
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-bottom: 1px solid #e2e8f0;
            position: relative;
            z-index: 10;
        }

        .header-left h2 {
            color: #2d3748;
            font-size: 24px;
            font-weight: 700;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 10px 15px 10px 45px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            width: 300px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 13px;
            gap: 6px;
        }
        .btn-info {
            background-color: #3ab0e2;
            color: white;
        }
        .btn-info:hover {
            background-color: #2c99c8;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
        }
        .btn-secondary:hover {
            background-color: #cbd5e0;
        }
        .btn-danger {
            background-color: #f56565;
            color: white;
        }
        .btn-danger:hover {
            background-color: #e53e3e;
        }
        .btn-warning {
            background-color: #f6e05e;
            color: #744210;
        }
        .btn-warning:hover {
            background-color: #f5cb1e;
        }


        .user-profile {
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 20px;
            transition: background 0.3s ease;
        }

        .user-profile:hover {
            background: #f1f5f9;
        }

        .avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 160px;
            overflow: hidden;
            margin-top: 8px;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            padding: 12px 18px;
            font-size: 14px;
            color: #374151;
            text-decoration: none;
            display: block;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f3f4f6;
            color: #667eea;
        }

        .dropdown-divider {
            height: 1px;
            margin: 4px 0;
            overflow: hidden;
            background-color: #e5e7eb;
        }

        .content-area {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            background-color: #f8fafc;
        }

        .project-info-card {
            background-color: white;
            padding: 25px;
            border-radius: 0;
            box-shadow: none;
            margin-bottom: 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .project-info-header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        .project-info-header-actions .actions {
            display: flex;
            gap: 10px;
        }


        .project-info-header {
            font-size: 18px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-item-label {
            font-size: 13px;
            color: #718096;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .info-item-value {
            font-size: 15px;
            color: #2d3748;
            font-weight: 600;
            padding: 8px 12px;
            background-color: #f7fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }

        .tabs-container {
            background-color: white;
            border-radius: 0;
            box-shadow: none;
            overflow: hidden;
            height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
        }

        .tab-navigation {
            display: flex;
            background-color: #edf2f7;
            border-bottom: 1px solid #e2e8f0;
            padding: 0 15px;
            flex-wrap: wrap;
        }

        .tab-button {
            padding: 15px 20px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 14px;
            font-weight: 500;
            color: #4a5568;
            transition: all 0.3s ease;
            position: relative;
            border-bottom: 3px solid transparent;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .tab-button:hover {
            color: #667eea;
        }

        .tab-button.active {
            color: #667eea;
            font-weight: 600;
            border-bottom: 3px solid #667eea;
        }
        .tab-button svg {
            width: 16px;
            height: 16px;
        }

        .tab-content {
            padding: 25px;
            flex: 1;
            overflow-y: auto;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }

        .sub-tab-navigation {
            display: flex;
            gap: 0px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
            padding: 0 10px;
        }
        .sub-tab-button {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 14px;
            font-weight: 500;
            color: #4a5568;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            margin-bottom: -1px;
        }
        .sub-tab-button:hover {
            color: #667eea;
        }
        .sub-tab-button.active {
            color: #667eea;
            font-weight: 600;
            border-bottom: 2px solid #667eea;
        }

        .sub-tab-pane {
            display: none;
        }
        .sub-tab-pane.active {
            display: block;
        }

        .placeholder-text {
            color: #718096;
            font-size: 16px;
            text-align: center;
            padding: 40px 20px;
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            background-color: #fdfdfe;
        }
        .placeholder-form-notice {
            color: #4a5568;
            font-size: 14px;
            text-align: left;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: #f9fafb;
            margin-top: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .data-table th, .data-table td {
            border: 1px solid #e2e8f0;
            padding: 12px 15px;
            text-align: left;
            font-size: 14px;
            vertical-align: middle;
        }
        .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #4a5568;
        }
        .data-table td {
            color: #2d3748;
        }
        .data-table tr:nth-child(even) {
            background-color: #fdfdfe;
        }
        .data-table tr:hover {
            background-color: #f1f5f9;
        }
        .table-actions button {
            margin-right: 5px;
        }
        .table-actions button:last-child {
            margin-right: 0;
        }
        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .tab-header.sub-header {
            margin-top: 0;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: none;
        }
        .tab-header h4 {
            font-size: 18px;
            color: #374151;
            font-weight: 600;
        }
        .tab-header h5 {
            font-size: 16px;
            color: #4a5568;
            font-weight: 600;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-dialog {
            position: relative;
            margin: 50px auto;
            padding: 0;
            width: 90%;
            max-width: 600px;
            pointer-events: none;
        }
        .modal-dialog.modal-lg {
            max-width: 800px;
        }

        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            pointer-events: auto;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0,0,0,.2);
            border-radius: .5rem;
            outline: 0;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
        }
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: calc(.5rem - 1px);
            border-top-right-radius: calc(.5rem - 1px);
        }
        .modal-title {
            margin-bottom: 0;
            line-height: 1.5;
            font-size: 1.25rem;
            font-weight: 600;
            color: #343a40;
        }
        .modal-close-btn {
            padding: 0.5rem;
            margin: -0.5rem -0.5rem -0.5rem auto;
            background-color: transparent;
            border: 0;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: .5;
            cursor: pointer;
        }
        .modal-close-btn:hover {
            opacity: .75;
        }
        .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 1.5rem;
        }
        .modal-footer {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-end;
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            border-bottom-right-radius: calc(.5rem - 1px);
            border-bottom-left-radius: calc(.5rem - 1px);
        }
        .modal-footer > :not(:first-child) {
            margin-left: .5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: inline-block;
            margin-bottom: .5rem;
            font-weight: 500;
            color: #495057;
        }
        .form-control {
            display: block;
            width: 100%;
            padding: .5rem .75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: .375rem;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        }
        .form-control:focus {
            color: #495057;
            background-color: #fff;
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 .2rem rgba(102,126,234,.25);
        }
        .form-control select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right .75rem center;
            background-size: 16px 12px;
        }
        .service-selection-list .service-item {
            display: block;
            margin-bottom: .25rem;
        }
        .service-selection-list .service-item input {
            margin-right: .5rem;
        }
        .service-config-group h6 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: .75rem;
            color: #667eea;
        }
        .env-history-controls {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        .env-history-table td input[type="checkbox"] {
            margin-right: 5px;
            vertical-align: middle;
        }
        .info-box {
            background-color: #f1f5f9;
            border-radius: .375rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 14px;
            color: #4a5568;
            line-height: 1.6;
        }
        .info-box code {
            background-color: #e2e8f0;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Service Management Styles */
        .service-editor-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            height: 500px;
        }
        .service-editor-left {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .service-editor-right {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .code-editor {
            flex: 1;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            padding: 15px;
            background-color: #f8fafc;
            resize: none;
            line-height: 1.5;
        }
        .code-editor:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .service-variables-container {
            flex: 1;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8fafc;
            overflow-y: auto;
        }
        .variable-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background-color: white;
        }
        .variable-group h6 {
            margin: 0 0 10px 0;
            color: #667eea;
            font-size: 14px;
            font-weight: 600;
        }
        .variable-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 10px;
        }
        .variable-item label {
            min-width: 120px;
            font-size: 13px;
            color: #4a5568;
            margin: 0;
        }
        .variable-item input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 13px;
        }
        .import-from-code-btn {
            margin-top: 10px;
            margin-bottom: 20px;
        }
        .modal-dialog.modal-xl {
            max-width: 95%;
            width: 95%;
        }
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }
        .radio-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .radio-item input[type="radio"] {
            margin: 0;
            width: 16px;
            height: 16px;
            accent-color: #667eea;
        }
        .radio-item label {
            cursor: pointer;
            color: #4a5568;
            transition: color 0.2s ease;
        }
        .radio-item input[type="radio"]:checked + label {
            color: #667eea;
            font-weight: 500;
        }

        /* Service Release Form Styles */
        .release-form-container {
            padding: 24px;
            background-color: #fff;
        }

        .release-form-group {
            margin-bottom: 20px;
        }

        .release-form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }

        .release-form-input,
        .release-form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            box-sizing: border-box;
        }

        .release-form-input:focus,
        .release-form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .release-form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .select-services-wrapper {
            display: flex;
            align-items: center;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            position: relative;
            background-color: #fff;
            min-height: 40px;
        }

        .select-services-wrapper.focused {
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            flex-grow: 1;
            align-items: center;
        }

        .dropdown-arrow {
            width: 16px;
            height: 16px;
            color: #888;
            margin-left: 8px;
        }

        .services-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dcdcdc;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .service-option {
            padding: 10px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s ease;
        }

        .service-option:hover {
            background-color: #f5f5f5;
        }

        .service-option input[type="checkbox"] {
            margin: 0;
        }

        .service-card {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 25px;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            position: relative;
        }

        .service-card.hidden {
            display: none;
        }

        .service-card-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f1f5f9;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            font-size: 16px;
            line-height: 1;
        }

        .service-card-close:hover {
            background-color: #e2e8f0;
            color: #475569;
        }

        .service-card-title {
            font-size: 17px;
            font-weight: 600;
            color: #333;
            margin-bottom: 18px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }

        .image-selection-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 12px;
        }

        .image-select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background-color: white;
            margin-bottom: 20px;
        }

        .image-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .resource-detection-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 12px;
        }

        .resource-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
        }

        .resource-item svg {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            color: #4a5568;
        }

        .variable-config-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-top: 25px;
            margin-bottom: 12px;
        }

        .variable-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .variable-label {
            font-size: 14px;
            color: #333;
            width: 90px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .variable-input-field {
            flex-grow: 1;
            padding: 8px 10px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
        }

        .question-icon {
            width: 14px;
            height: 14px;
            color: #999;
            cursor: help;
        }


        /* 状态圆点 */
        .svc-status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 6px;
            vertical-align: middle;
        }
        .svc-status-running { background: #38c172; }
        .svc-status-unstable { background: #f6ad55; }
        .svc-status-stopped { background: #e53e3e; }
        /* 镜像、外网访问省略号 */
        .svc-ellipsis {
            display: inline-block;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: middle;
        }

    </style>
</head>
<body>
<div class="container">
    <div class="sidebar">
        <div class="logo">
            <h1>DevOps</h1>
            <p>Platform</p>
        </div>

        <div class="nav-section">
            <div class="nav-title">项目中心</div>
            <div class="nav-items">
                <a href="#" class="nav-item" onclick="navigateTo('index.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2zm4 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                    </svg>
                    <span class="nav-text">项目概览</span>
                </a>
                <a href="#" class="nav-item active" onclick="navigateTo('project-list.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                    </svg>
                    <span class="nav-text">项目列表</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-title">产物中心</div>
            <div class="nav-items">
                <a href="#" class="nav-item" onclick="navigateTo('image-center.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                    </svg>
                    <span class="nav-text">镜像仓库</span>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('build-info.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
                    </svg>
                    <span class="nav-text">编译构建中心</span>
                </a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <div class="header-left">
                <h2>My Awesome Project</h2>
            </div>
            <div class="header-right">
                <div class="user-profile" id="userProfileDropdownToggle">
                    <div class="avatar">张</div>
                    <span>张三</span>
                    <div class="dropdown-menu" id="userProfileDropdown">
                        <a href="#" class="dropdown-item" onclick="customAlert('跳转到后台管理页面 (功能待实现)')">后台管理</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" onclick="customAlert('退出账号 (功能待实现)')">退出账号</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-area">

            <div class="tabs-container">
                <div class="tab-navigation">
                    <button class="tab-button active" onclick="openTab(event, 'serviceManagement')">
                        <svg fill="currentColor" viewBox="0 0 16 16"><path d="M1.596 5.165A.5.5 0 0 1 2 4.929V2.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 .5.5v2.429a.5.5 0 0 1-.404.492L8.404 6.413a.5.5 0 0 1-.808 0L1.596 5.165zM2 6.429l5.596 2.928a.5.5 0 0 0 .808 0L14 6.429V10.5a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5V6.429zM1 11.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2z"/></svg>
                        应用管理
                    </button>
                    <button class="tab-button" onclick="openTab(event, 'compileBuild')">
                        <svg fill="currentColor" viewBox="0 0 16 16">
                            <path d="M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V2zm1.5 0a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1h-13zM0 6a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V6zm1.5 0a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1h-13zM0 10a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1v-2zm1.5 0a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1h-13z"/>
                            <path d="M5 2.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5V4h-2V2.5zm3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5V4h-2V2.5zm3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5V4h-2V2.5z"/>
                        </svg>
                        编译构建
                    </button>
                    <button class="tab-button" onclick="openTab(event, 'workflow')">
                        <svg fill="currentColor" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M6 3.5A1.5 1.5 0 0 1 7.5 2h1A1.5 1.5 0 0 1 10 3.5v1A1.5 1.5 0 0 1 8.5 6v1.5a.5.5 0 0 1-1 0V6A1.5 1.5 0 0 1 6 4.5v-1zM8.5 5a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1zM3 11.5A1.5 1.5 0 0 1 4.5 10h1A1.5 1.5 0 0 1 7 11.5v1A1.5 1.5 0 0 1 5.5 14h-1A1.5 1.5 0 0 1 3 12.5v-1zm1.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1zm3.5-1.5h1.5a.5.5 0 0 1 0 1H8a.5.5 0 0 1 0-1zm1.5 1.5A1.5 1.5 0 0 1 11.5 10h1a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1-1.5 1.5h-1a1.5 1.5 0 0 1-1.5-1.5v-1zm1.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1z"/>
                            <path d="M8 7.5a.5.5 0 0 1 .5-.5h3.5a.5.5 0 0 1 0 1H8.5V10h-1V7.5A.5.5 0 0 1 8 7.5zM4.5 6a.5.5 0 0 0-.5.5v1.5a.5.5 0 0 0 1 0V7h3.5a.5.5 0 0 0 .5-.5A.5.5 0 0 0 8.5 6h-4z"/>
                        </svg>
                        工作流
                    </button>
                    <button class="tab-button" onclick="openTab(event, 'environmentManagement')">
                        <svg fill="currentColor" viewBox="0 0 16 16"><path d="M1.558 1.778C1.036 2.29.613 3.066.31 4.016a.5.5 0 0 0 .51.656A12.99 12.99 0 0 1 8 4c2.09 0 4.024.483 5.71 1.325a.5.5 0 0 0 .51-.656c-.303-.95-.726-1.727-1.248-2.24C11.497 1.087 9.83 1 8 1s-3.497.087-4.972.635L1.558 1.778zM.132 6.308A.5.5 0 0 0 .61 6.78c.408.45.84.863 1.294 1.238A13.03 13.03 0 0 0 8 9c2.49 0 4.78-.662 6.626-1.74a.5.5 0 0 0-.07-.912c-.453-.375-.886-.788-1.294-1.238a.5.5 0 0 0-.73-.03L8.5 7.143a.5.5 0 0 1-.514.002L3.503 5.22a.5.5 0 0 0-.692.07L.132 6.308zM8 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0 1a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM3.108 11.67A.5.5 0 0 0 3.5 12h9a.5.5 0 0 0 .392-.83L8.69 7.856a.5.5 0 0 0-.782.001L3.108 11.67zM15.69 4.016c.303.95.726 1.727 1.248 2.24A.5.5 0 0 1 16.5 7c0 .276-.224.5-.5.5a.504.504 0 0 1-.472-.343c-.513-.503-.926-1.255-1.223-2.182A14.01 14.01 0 0 0 8 3c-2.228 0-4.295.53-6.043 1.475C1.633 3.667 1.23 2.934.733 2.43A.5.5 0 0 1 .5 2c0-.276.224-.5.5-.5c.026 0 .05.008.076.012C2.503.99 4.17 1.5 8 1.5s5.497-.51 6.924-1.012A.498.498 0 0 1 15.5 0c.276 0 .5.224.5.5a.498.498 0 0 1-.31.472c-.303.95-.726 1.727-1.248 2.24a12.99 12.99 0 0 1-5.71 1.325.5.5 0 0 1-.51-.656C6.024 3.483 4.09 3 2 3 .613 3 .29 3.29 0 3.766V11.5a1.5 1.5 0 0 0 1.5 1.5h13A1.5 1.5 0 0 0 16 11.5V3.766C15.71 3.29 15.387 3 14 3c-2.09 0-4.024.483-5.71 1.325a.5.5 0 0 1-.51-.656C8.082 2.72 9.909 2 14 2c1.387 0 1.71.29 2 .766V11.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V3.766C.29 3.29.613 3 2 3c2.09 0 4.024.483 5.71 1.325a.5.5 0 0 1 .51-.656A12.99 12.99 0 0 1 14 4c1.918 0 3.756.428 5.425 1.164a.5.5 0 0 1 .267.628L13.29 14.01a.5.5 0 0 1-.895-.04L8.5 5.143a.5.5 0 0 0-.998 0L3.605 13.97a.5.5 0 0 1-.895.04L.308 5.792a.5.5 0 0 1 .267-.628C2.244 4.428 4.082 4 6 4c2.09 0 4.024.483 5.71 1.325a.5.5 0 0 0 .51.656A12.99 12.99 0 0 0 14 6c1.918 0 3.756-.428 5.425-1.164A.5.5 0 0 0 16 4.208V1.5A1.5 1.5 0 0 0 14.5 0h-13A1.5 1.5 0 0 0 0 1.5v2.708a.5.5 0 0 0 .308.628C2.244 5.572 4.082 6 6 6c1.918 0 3.756-.428 5.425-1.164A.5.5 0 0 1 12 4.208V1.5a.5.5 0 0 1 .5-.5h.5a.5.5 0 0 1 .5.5v2.708a.5.5 0 0 0 .308.628C15.756 5.572 17.918 6 20 6V1.5A1.5 1.5 0 0 0 18.5 0h-13A1.5 1.5 0 0 0 4 1.5v2.708A14.01 14.01 0 0 1 8 3a14.01 14.01 0 0 1 4 .708V1.5A.5.5 0 0 0 11.5 1h-3A.5.5 0 0 0 8 1.5v2.208C6.244 4.428 4.082 5 2 5c-1.918 0-3.756-.428-5.425-1.164A.5.5 0 0 1-4 .208V1.5A1.5 1.5 0 0 1-2.5 0h13A1.5 1.5 0 0 1 12 1.5v2.708A12.99 12.99 0 0 0 8 4a12.99 12.99 0 0 0-4 .708V1.5A.5.5 0 0 1 4.5 1h-3A.5.5 0 0 1 1 1.5v2.708c1.669.736 3.506 1.164 5.425 1.164.112 0 .223-.004.333-.012a.5.5 0 0 1 .371.114l4.98 3.985a.5.5 0 0 1 .01.716l-5.5 4.4a.5.5 0 0 1-.618 0l-5.5-4.4a.5.5 0 0 1 .01-.716l4.98-3.985a.5.5 0 0 1 .371-.114A14.01 14.01 0 0 0 8 6c1.918 0 3.756.428 5.425 1.164.11.008.22.012.333.012 1.919 0 3.756-.428 5.425-1.164.11-.008.22-.012.333-.012V11.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 11.5V7.177c.11.008.22.012.333.012.222 0 .44-.008.66-.025a.5.5 0 0 1 .475.397l.666 2.666a.5.5 0 0 0 .94-.236l-.666-2.666a.5.5 0 0 1 .475-.633c.22-.017.438-.025.66-.025.222 0 .44.008.66.025a.5.5 0 0 1 .475.397l.666 2.666a.5.5 0 0 0 .94-.236l-.666-2.666a.5.5 0 0 1 .475-.633c.22-.017.438-.025.66-.025.222 0 .44.008.66.025a.5.5 0 0 1 .475.397l.666 2.666a.5.5 0 0 0 .94-.236l-.666-2.666a.5.5 0 0 1 .475-.633c.22-.017.438-.025.66-.025s.44.008.66.025a.5.5 0 0 1 .475.397l.666 2.666a.5.5 0 0 0 .94-.236l-.666-2.666a.5.5 0 0 1 .475-.633c.22-.017.438-.025.66-.025.111 0 .222.004.333.012V6.008C17.756 5.272 15.918 5 14 5c-1.918 0-3.756.428-5.425 1.164A.5.5 0 0 1 8 6.792V11.5a.5.5 0 0 0 .5.5h7a.5.5 0 0 0 .5-.5V6.792a.5.5 0 0 1-.575-.628C16.244 5.428 18.082 5 20 5v-.823c-.111.008-.222.012-.333.012-.222 0-.44-.008-.66-.025a.5.5 0 0 0-.475.397l-.666 2.666a.5.5 0 0 1-.94-.236l.666-2.666a.5.5 0 0 0-.475-.633 11.96 11.96 0 0 0-.66-.025c-.222 0-.44.008-.66.025a.5.5 0 0 0-.475.397l-.666 2.666a.5.5 0 0 1-.94-.236l.666-2.666a.5.5 0 0 0-.475-.633 11.96 11.96 0 0 0-.66-.025c-.222 0-.44.008-.66.025a.5.5 0 0 0-.475.397l-.666 2.666a.5.5 0 0 1-.94-.236l.666-2.666a.5.5 0 0 0-.475-.633 11.96 11.96 0 0 0-.66-.025c-.111 0-.222.004-.333.012V4.008C2.244 3.272.082 3 0 3V1.5A1.5 1.5 0 0 1 1.5 0h13A1.5 1.5 0 0 1 16 1.5V3c0 .082-.244.272-1.925.992L8.5 8.857a.5.5 0 0 1-.998 0L1.925 3.992C.244 3.272 0 3.082 0 3V1.5A.5.5 0 0 1 .5 1h15a.5.5 0 0 1 .5.5V3c0 .082-.244.272-1.925.992L8.5 8.857a1.5 1.5 0 0 1-2.995 0L.075 3.992A.5.5 0 0 1 0 3.5V1.5A1.5 1.5 0 0 1 1.5 0h13A1.5 1.5 0 0 1 16 1.5v2c0 .582-2.244 1.728-7.925 4.492a.5.5 0 0 1-.15 0L0 3.5z"/></svg>
                        环境管理
                    </button>
                    <button class="tab-button" onclick="openTab(event, 'codeRepo')">
                        <svg fill="currentColor" viewBox="0 0 16 16"><path d="M4.25 2A2.25 2.25 0 0 0 2 4.25v3.5A2.25 2.25 0 0 0 4.25 10h1.5a.75.75 0 0 1 0 1.5h-1.5A3.75 3.75 0 0 1 .5 7.75v-3.5C.5 2.477 2.477.5 4.25.5h7.5C13.523.5 15.5 2.477 15.5 4.25v7.5A3.75 3.75 0 0 1 11.75 15.5h-3.5a.75.75 0 0 1 0-1.5h3.5A2.25 2.25 0 0 0 14 11.75v-7.5A2.25 2.25 0 0 0 11.75 2h-7.5z"/><path d="M4.25 10a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zM3 10a.5.5 0 1 1 1 0 .5.5 0 0 1-1 0zm2.5-1.5a.5.5 0 1 1 0 1 .5.5 0 0 1 0-1zM11.75 4a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0z"/></svg>
                        代码管理
                    </button>
                    <button class="tab-button" onclick="openTab(event, 'projectSettings')">
                        <svg fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                            <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
                        </svg>
                        项目设置
                    </button>
                </div>

                <div id="codeRepo" class="tab-content tab-pane">
                    <div class="tab-header">
                        <h4>代码仓库列表</h4>
                        <button class="btn btn-primary btn-sm" onclick="openModal('codeRepoModal', 'add')">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>
                            新增
                        </button>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">管理本项目关联的代码仓库，用于编译构建和代码扫描。</p>
                    <table class="data-table">
                        <thead>
                        <tr>
                            <th>标识</th>
                            <th>URL</th>
                            <th>授权信息</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>my-gitlab</td>
                            <td>https://gitlab.example.com</td>
                            <td>Application ID: ●●●●●●●●</td>
                            <td>2025-05-20 11:45</td>
                            <td class="table-actions">
                                <button class="btn btn-secondary btn-sm" onclick="openModal('codeRepoModal', 'edit', {id: 'my-gitlab', source: 'GitLab', url: 'https://gitlab.example.com', appId: 'abc-123', secret: 'xyz-789'})">编辑</button>
                                <button class="btn btn-danger btn-sm" onclick="customAlert('确认删除代码仓库 my-gitlab?')">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>corp-github</td>
                            <td>https://github.com/corp-org</td>
                            <td>Application ID: ●●●●●●●●</td>
                            <td>2025-03-10 09:30</td>
                            <td class="table-actions">
                                <button class="btn btn-secondary btn-sm" onclick="openModal('codeRepoModal', 'edit', {id: 'corp-github', source: 'GitHub', url: 'https://github.com/corp-org', appId: 'def-456', secret: 'uvw-456'})">编辑</button>
                                <button class="btn btn-danger btn-sm" onclick="customAlert('确认删除代码仓库 corp-github?')">删除</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>


                <div id="environmentManagement" class="tab-content tab-pane">
                    <div class="tab-header">
                        <h4>环境列表</h4>
                        <button class="btn btn-primary btn-sm" onclick="openModal('newEnvironmentModal')">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>
                            新建环境
                        </button>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">管理本项目关联的部署环境（如开发、测试、生产环境）及其对应的集群信息。</p>
                    <table class="data-table">
                        <thead>
                        <tr>
                            <th>环境名称</th>
                            <th>集群类型</th>
                            <th>关联集群</th>
                            <th>命名空间</th>
                            <th>环境状态</th>
                            <th>服务数 (运行中/总数)</th>
                            <th>更新时间</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>开发环境</td>
                            <td>Kubernetes</td>
                            <td>dev-cluster-01</td>
                            <td>project-awesome-dev</td>
                            <td style="color: green;">running</td>
                            <td>5/5</td>
                            <td>2025-06-05 14:30</td>
                            <td class="table-actions">
                                <button class="btn btn-info btn-sm" onclick="openModal('environmentDetailModal', '开发环境')">详情</button>
                                <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                <button class="btn btn-danger btn-sm" onclick="handleDeleteEnvironment('开发环境')">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>测试环境</td>
                            <td>Kubernetes</td>
                            <td>qa-cluster-ap-south</td>
                            <td>project-awesome-qa</td>
                            <td style="color: orange;">unstable</td>
                            <td>3/4</td>
                            <td>2025-06-04 18:00</td>
                            <td class="table-actions">
                                <button class="btn btn-info btn-sm" onclick="openModal('environmentDetailModal', '测试环境')">详情</button>
                                <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                <button class="btn btn-danger btn-sm" onclick="handleDeleteEnvironment('测试环境')">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>生产环境</td>
                            <td>Kubernetes</td>
                            <td>prod-cluster-us-east</td>
                            <td>project-awesome-prod</td>
                            <td style="color: green;">running</td>
                            <td>5/5</td>
                            <td>2025-06-01 09:00</td>
                            <td class="table-actions">
                                <button class="btn btn-info btn-sm" onclick="openModal('environmentDetailModal', '生产环境')">详情</button>
                                <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                <button class="btn btn-danger btn-sm" onclick="handleDeleteEnvironment('生产环境')">删除</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div id="serviceManagement" class="tab-content tab-pane active">
                    <div class="tab-header">
                        <h4>应用管理</h4>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 10px;">管理项目的服务发布和应用配置。</p>

                    <div class="sub-tab-navigation">
                        <button class="sub-tab-button active" onclick="openServiceSubTab(event, 'serviceReleaseView')">发布记录</button>
                        <button class="sub-tab-button" onclick="openServiceSubTab(event, 'serviceManageView')">服务管理</button>
                    </div>

                    <div id="serviceReleaseView" class="sub-tab-pane active">
                        <div class="tab-header sub-header">
                            <h5>发布记录</h5>
                        </div>
                        <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">
                            查看该项目的所有服务发布历史记录，包括发布时间、环境、状态等信息。
                        </p>

                        <!-- 筛选框 -->
                        <div style="display: flex; gap: 15px; margin-bottom: 20px; align-items: center; flex-wrap: wrap;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; color: #4a5568; white-space: nowrap;">服务:</label>
                                <select class="form-control" style="width: 150px; font-size: 14px;">
                                    <option value="">全部服务</option>
                                    <option value="frontend">frontend</option>
                                    <option value="backend">backend</option>
                                    <option value="database">database</option>
                                    <option value="cache">cache</option>
                                </select>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; color: #4a5568; white-space: nowrap;">环境:</label>
                                <select class="form-control" style="width: 150px; font-size: 14px;">
                                    <option value="">全部环境</option>
                                    <option value="dev">开发环境</option>
                                    <option value="test">测试环境</option>
                                    <option value="prod">生产环境</option>
                                </select>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; color: #4a5568; white-space: nowrap;">发布人:</label>
                                <select class="form-control" style="width: 150px; font-size: 14px;">
                                    <option value="">全部发布人</option>
                                    <option value="张三">张三</option>
                                    <option value="李四">李四</option>
                                    <option value="王五">王五</option>
                                    <option value="赵六">赵六</option>
                                    <option value="孙七">孙七</option>
                                </select>
                            </div>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('筛选功能待实现')" style="margin-left: 10px;">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                                    <path d="M6 10.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                </svg>
                                筛选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('重置筛选条件')">
                                重置
                            </button>
                            <button id="compareVersionsBtn" class="btn btn-success btn-sm" onclick="compareSelectedVersions()" disabled style="margin-left: 10px;">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                                    <path d="M1.5 1.5A.5.5 0 0 1 2 1h4.5a.5.5 0 0 1 0 1H2v4.5a.5.5 0 0 1-1 0V1.5zm13 0a.5.5 0 0 0-.5-.5H9a.5.5 0 0 0 0 1h4.5V6.5a.5.5 0 0 0 1 0V1.5zM2 14.5a.5.5 0 0 1-.5-.5V9a.5.5 0 0 1 1 0v4.5H6.5a.5.5 0 0 1 0 1H2zm12.5-.5a.5.5 0 0 0 .5-.5V9a.5.5 0 0 0-1 0v4.5H9a.5.5 0 0 0 0 1h4.5z"/>
                                    <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                                </svg>
                                版本对比 (<span id="selectedCount">0</span>)
                            </button>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAllReleases" onchange="toggleAllReleases(this)" style="margin: 0;">
                                    </th>
                                    <th>服务名称</th>
                                    <th>目标环境</th>
                                    <th>版本号</th>
                                    <th>镜像版本</th>
                                    <th>发布备注</th>
                                    <th>发布时间</th>
                                    <th>发布人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="release-checkbox" data-service="frontend" data-environment="生产环境" data-version="v2.1.0" onchange="updateCompareButton()" style="margin: 0;">
                                    </td>
                                    <td>frontend</td>
                                    <td>生产环境</td>
                                    <td>v2.1.0</td>
                                    <td>frontend:v2.1.0</td>
                                    <td>修复登录页面样式问题</td>
                                    <td>2024-12-05 14:30</td>
                                    <td>张三</td>
                                    <td class="table-actions">
                                        <button class="btn btn-info btn-sm" onclick="openReleaseDetailModal('frontend', 'v2.1.0', '生产环境')">详情</button>
                                        <button class="btn btn-warning btn-sm" onclick="customAlert('回滚 frontend 到上一版本')">回滚</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="release-checkbox" data-service="backend" data-environment="测试环境" data-version="v1.3.2" onchange="updateCompareButton()" style="margin: 0;">
                                    </td>
                                    <td>backend</td>
                                    <td>测试环境</td>
                                    <td>v1.3.2</td>
                                    <td>backend:v1.3.2</td>
                                    <td>新增用户权限管理功能</td>
                                    <td>2024-12-05 15:45</td>
                                    <td>李四</td>
                                    <td class="table-actions">
                                        <button class="btn btn-info btn-sm" onclick="openReleaseDetailModal('backend', 'v1.3.2', '测试环境')">详情</button>
                                        <button class="btn btn-warning btn-sm" onclick="customAlert('回滚 backend 到上一版本')">回滚</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="release-checkbox" data-service="frontend" data-environment="测试环境" data-version="v2.0.9" onchange="updateCompareButton()" style="margin: 0;">
                                    </td>
                                    <td>frontend</td>
                                    <td>测试环境</td>
                                    <td>v2.0.9</td>
                                    <td>frontend:v2.0.9</td>
                                    <td>优化页面加载性能</td>
                                    <td>2024-12-04 16:15</td>
                                    <td>赵六</td>
                                    <td class="table-actions">
                                        <button class="btn btn-info btn-sm" onclick="openReleaseDetailModal('frontend', 'v2.0.9', '测试环境')">详情</button>
                                        <button class="btn btn-warning btn-sm" onclick="customAlert('回滚 frontend 到上一版本')">回滚</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="release-checkbox" data-service="cache" data-environment="生产环境" data-version="v7.0.1" onchange="updateCompareButton()" style="margin: 0;">
                                    </td>
                                    <td>cache</td>
                                    <td>生产环境</td>
                                    <td>v7.0.1</td>
                                    <td>redis:7.0</td>
                                    <td>升级Redis版本，提升稳定性</td>
                                    <td>2024-12-03 09:30</td>
                                    <td>孙七</td>
                                    <td class="table-actions">
                                        <button class="btn btn-info btn-sm" onclick="openReleaseDetailModal('cache', 'v7.0.1', '生产环境')">详情</button>
                                        <button class="btn btn-warning btn-sm" onclick="customAlert('回滚 cache 到上一版本')">回滚</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- 分页组件 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 0 5px;">
                            <div style="font-size: 14px; color: #718096;">
                                显示第 1-4 条，共 4 条记录
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <span style="font-size: 14px; color: #4a5568;">每页显示:</span>
                                    <select class="form-control" style="width: 80px; font-size: 14px; padding: 4px 8px;">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <button class="btn btn-outline-secondary btn-sm" disabled style="padding: 4px 8px; font-size: 12px;">
                                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
                                        </svg>
                                        上一页
                                    </button>
                                    <div style="display: flex; gap: 2px;">
                                        <button class="btn btn-primary btn-sm" style="padding: 4px 8px; font-size: 12px; min-width: 32px;">1</button>
                                        <button class="btn btn-outline-secondary btn-sm" style="padding: 4px 8px; font-size: 12px; min-width: 32px;" onclick="customAlert('跳转到第2页')">2</button>
                                        <button class="btn btn-outline-secondary btn-sm" style="padding: 4px 8px; font-size: 12px; min-width: 32px;" onclick="customAlert('跳转到第3页')">3</button>
                                        <span style="padding: 4px 8px; font-size: 12px; color: #718096;">...</span>
                                        <button class="btn btn-outline-secondary btn-sm" style="padding: 4px 8px; font-size: 12px; min-width: 32px;" onclick="customAlert('跳转到最后一页')">3</button>
                                    </div>
                                    <button class="btn btn-outline-secondary btn-sm" style="padding: 4px 8px; font-size: 12px;" onclick="customAlert('跳转到下一页')">
                                        下一页
                                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="serviceManageView" class="sub-tab-pane">
                        <div class="tab-header sub-header">
                            <h5>服务列表</h5>
                            <button class="btn btn-primary btn-sm" onclick="openModal('serviceModal', 'add')">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>
                                添加服务
                            </button>
                        </div>
                        <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">管理本项目的应用配置，支持从代码源导入或手动配置。</p>
                        <table class="data-table">
                            <thead>
                            <tr>
                                <th>服务名称</th>
                                <th>来源</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>frontend-service</td>
                                <td>代码源</td>
                                <td>2025-06-05 14:30</td>
                                <td class="table-actions">
                                    <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                    <button class="btn btn-info btn-sm" onclick="customAlert('查看 frontend-service 环境分布')">环境分布</button>
                                    <button class="btn btn-secondary btn-sm" onclick="openModal('serviceModal', 'edit', 'frontend-service')">编辑</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                    <button class="btn btn-info btn-sm">服务版本</button>
                                </td>
                            </tr>
                            <tr>
                                <td>backend-api-service</td>
                                <td>手动输入</td>
                                <td>2025-06-04 10:15</td>
                                <td class="table-actions">
                                    <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                    <button class="btn btn-info btn-sm" onclick="customAlert('查看 backend-api-service 环境分布')">环境分布</button>
                                    <button class="btn btn-secondary btn-sm" onclick="openModal('serviceModal', 'edit', 'backend-api-service')">编辑</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                    <button class="btn btn-info btn-sm">服务版本</button>
                                </td>
                            </tr>
                            <tr>
                                <td>database-service</td>
                                <td>代码源</td>
                                <td>2025-06-03 16:20</td>
                                <td class="table-actions">
                                    <button class="btn btn-primary btn-sm" onclick="openModal('deployToEnvironmentModal')">发布</button>
                                    <button class="btn btn-info btn-sm" onclick="customAlert('查看 database-service 环境分布')">环境分布</button>
                                    <button class="btn btn-secondary btn-sm" onclick="openModal('serviceModal', 'edit', 'database-service')">编辑</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                    <button class="btn btn-info btn-sm">服务版本</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


                <div id="compileBuild" class="tab-content tab-pane">
                    <div class="tab-header">
                        <h4>编译构建</h4>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 10px;">管理项目的构建模板和查看构建任务历史。</p>

                    <div class="sub-tab-navigation">
                        <button class="sub-tab-button active" onclick="openCompileBuildSubTab(event, 'buildTemplatesView')">构建模板</button>
                        <button class="sub-tab-button" onclick="openCompileBuildSubTab(event, 'buildTasksView')">我的构建任务</button>
                    </div>


                    <div id="buildTemplatesView" class="sub-tab-pane active">
                        <div class="tab-header sub-header">
                            <h5>构建模板列表</h5>
                            <button class="btn btn-primary btn-sm" onclick="customAlert('功能待实现: 新增构建模板 (将打开新建构建模板表单)')">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>
                                新增构建模板
                            </button>
                        </div>
                        <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">管理可用于执行构建任务的模板。</p>
                        <table class="data-table">
                            <thead>
                            <tr>
                                <th>构建模板名称</th>
                                <th>对应服务</th>
                                <th>构建类型</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>build-frontend-template</td>
                                <td>frontend-service</td>
                                <td>镜像 (Docker)</td>
                                <td class="table-actions">
                                    <button class="btn btn-primary btn-sm" onclick="customAlert('执行构建 build-frontend-template')">执行</button>
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('编辑 build-frontend-template (将打开编辑构建模板表单)')">编辑</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('删除 build-frontend-template')">删除</button>
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 build-frontend-template 构建记录')">构建记录</button>
                                </td>
                            </tr>
                            <tr>
                                <td>build-backend-api-template</td>
                                <td>backend-api-service</td>
                                <td>二进制 (Go Binary)</td>
                                <td class="table-actions">
                                    <button class="btn btn-primary btn-sm" onclick="customAlert('执行构建 build-backend-api-template')">执行</button>
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('编辑 build-backend-api-template (将打开编辑构建模板表单)')">编辑</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('删除 build-backend-api-template')">删除</button>
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 build-backend-api-template 构建记录')">构建记录</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="placeholder-form-notice">
                            <strong>提示:</strong> 点击"新增构建模板"或"编辑"按钮，将会加载一个表单界面 (如下图参考所示)，用于配置构建模板的详细信息，如：构建名称、关联服务、基础设置 (Kubernetes/主机)、操作系统、依赖包、代码源信息 (Git仓库、分支、凭证)、环境变量、以及通用构建脚本等。
                            <div style="text-align:center; margin-top:15px;">
                                [此处为用户提供的参考图占位符 - 新建/编辑构建模板表单界面]
                            </div>
                        </div>
                    </div>

                    <div id="buildTasksView" class="sub-tab-pane">
                        <div class="tab-header sub-header">
                            <h5>我的构建任务</h5>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('功能待实现: 执行新构建任务 (选择模板并执行)')">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;"><path d="M10.804 8 5 4.696v6.608L10.804 8zm.792-.696a.802.802 0 0 1 0 1.392l-6.363 3.692C4.713 12.69 4 12.345 4 11.692V4.308c0-.653.713-.998 1.233-.696l6.363 3.692z"/></svg>
                                执行新构建任务
                            </button>
                        </div>
                        <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">查看您已执行或正在执行的构建任务记录。</p>
                        <table class="data-table">
                            <thead>
                            <tr>
                                <th>任务ID</th>
                                <th>关联构建模板</th>
                                <th>触发方式</th>
                                <th>状态</th>
                                <th>开始时间</th>
                                <th>耗时</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>task-001</td>
                                <td>build-frontend-template</td>
                                <td>手动</td>
                                <td style="color: green;">成功</td>
                                <td>2025-06-05 10:15</td>
                                <td>2m 10s</td>
                                <td class="table-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 task-001 日志')">查看日志</button>
                                </td>
                            </tr>
                            <tr>
                                <td>task-002</td>
                                <td>build-backend-template</td>
                                <td>Webhook (Git Push)</td>
                                <td style="color: blue;">运行中...</td>
                                <td>2025-06-05 15:00</td>
                                <td>-</td>
                                <td class="table-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 task-002 日志')">查看日志</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('取消 task-002')">取消</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

                <div id="workflow" class="tab-content tab-pane">
                    <div class="tab-header">
                        <h4>工作流列表</h4>
                        <button class="btn btn-primary btn-sm" onclick="customAlert('功能待实现: 创建工作流')">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>
                            创建工作流
                        </button>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">定义和管理项目的CI/CD工作流，设置触发条件和目标环境/服务。</p>
                    <table class="data-table">
                        <thead>
                        <tr>
                            <th>工作流名称</th>
                            <th>平均执行时间</th>
                            <th>成功率</th>
                            <th>最近执行状态</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>Deploy Frontend to Dev</td>
                            <td>5m 30s</td>
                            <td style="color: green;">95%</td>
                            <td style="color: green;">成功 (2025-06-05 10:20)</td>
                            <td class="table-actions">
                                <button class="btn btn-primary btn-sm" onclick="customAlert('立即执行 Deploy Frontend to Dev')">立即执行</button>
                                <button class="btn btn-secondary btn-sm" onclick="customAlert('配置 Deploy Frontend to Dev')">配置</button>
                                <button class="btn btn-danger btn-sm" onclick="customAlert('删除 Deploy Frontend to Dev')">删除</button>
                                <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 Deploy Frontend to Dev 执行历史')">执行历史</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Release API to Prod</td>
                            <td>12m 10s</td>
                            <td style="color: orange;">80%</td>
                            <td>等待执行</td>
                            <td class="table-actions">
                                <button class="btn btn-primary btn-sm" onclick="customAlert('立即执行 Release API to Prod')">立即执行</button>
                                <button class="btn btn-secondary btn-sm" onclick="customAlert('配置 Release API to Prod')">配置</button>
                                <button class="btn btn-danger btn-sm" onclick="customAlert('删除 Release API to Prod')">删除</button>
                                <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 Release API to Prod 执行历史')">执行历史</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div id="projectSettings" class="tab-content tab-pane">
                    <div class="tab-header">
                        <h4>项目设置</h4>
                    </div>
                    <p style="font-size: 13px; color: #718096; margin-bottom: 10px;">管理项目的基本信息和权限配置。</p>

                    <div class="sub-tab-navigation">
                        <button class="sub-tab-button active" onclick="openProjectSettingsSubTab(event, 'projectBasicInfo')">项目基本信息</button>
                        <button class="sub-tab-button" onclick="openProjectSettingsSubTab(event, 'projectPermissions')">项目权限管理</button>
                    </div>

                    <div id="projectBasicInfo" class="sub-tab-pane active">
                        <div class="project-info-card">
                            <div class="project-info-header-actions">
                                <span>项目基本信息</span>
                                <div class="actions">
                                    <button class="btn btn-secondary btn-sm" onclick="openModal('editProjectModal')">
                                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                                            <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
                                        </svg>
                                        编辑项目
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('确认删除项目 My Awesome Project? (功能待实现)')">
                                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/><path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/></svg>
                                        删除项目
                                    </button>
                                </div>
                            </div>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-item-label">项目名称:</span>
                                    <span class="info-item-value" id="projectInfoName">My Awesome Project</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-item-label">项目类型:</span>
                                    <span class="info-item-value" id="projectInfoType">Kubernetes (K8s)</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-item-label">项目Owner:</span>
                                    <span class="info-item-value">张三 (<EMAIL>)</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-item-label">创建时间:</span>
                                    <span class="info-item-value">2024-10-26 10:30 AM</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-item-label">项目描述:</span>
                                    <span class="info-item-value" id="projectInfoDescription">这是一个用于演示目的的K8s示例项目，包含多个微服务。</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="projectPermissions" class="sub-tab-pane">
                        <div class="tab-header sub-header">
                            <h5>项目成员与权限</h5>
                            <button class="btn btn-primary btn-sm" onclick="customAlert('功能待实现: 添加成员/组')">
                                <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16"><path d="M7 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/> <path fill-rule="evenodd" d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"/> <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/></svg>
                                添加成员
                            </button>
                        </div>
                        <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">管理本项目中的项目成员以及相关角色权限配置。</p>
                        <table class="data-table">
                            <thead>
                            <tr>
                                <th>项目成员</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>张三 (<EMAIL>)</td>
                                <td>管理员</td>
                                <td class="table-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('编辑 张三 的权限')">编辑</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('移除 张三')">移除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>开发团队A (用户组)</td>
                                <td>开发者</td>
                                <td class="table-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('编辑 开发团队A 的权限')">编辑</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('移除 开发团队A')">移除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>李四 (<EMAIL>)</td>
                                <td>测试工程师</td>
                                <td class="table-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="customAlert('编辑 李四 的权限')">编辑</button>
                                    <button class="btn btn-danger btn-sm" onclick="customAlert('移除 李四')">移除</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- Modals -->
<div id="codeRepoModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="codeRepoModalTitle">新增代码仓库</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('codeRepoModal')">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="codeRepoEditId">
                <div class="info-box">
                    - 应用授权的回调地址请填写: <br> <code>https://devops-test.scitix-inner.ai/api/directory/codehosts/callback</code><br>
                    - 应用权限请勾选: <code>api, read_user, read_repository</code><br>
                    - 详细配置可参考 <a>帮助文档</a>
                </div>
                <div class="form-group">
                    <label for="codeRepoSource">代码源:</label>
                    <select class="form-control" id="codeRepoSource">
                        <option value="GitLab" selected>GitLab</option>
                        <option value="GitHub">GitHub</option>
                        <option value="Gitee">Gitee</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="codeRepoIdentifier">代码源标识:</label>
                    <input type="text" class="form-control" id="codeRepoIdentifier" placeholder="请填写代码源标识">
                </div>
                <div class="form-group">
                    <label for="codeRepoUrl">GitLab 服务 URL:</label>
                    <input type="text" class="form-control" id="codeRepoUrl" placeholder="请输入URL, 包含协议">
                </div>
                <div class="form-group">
                    <label for="codeRepoAppId">Application ID:</label>
                    <input type="text" class="form-control" id="codeRepoAppId" placeholder="请输入Id">
                </div>
                <div class="form-group">
                    <label for="codeRepoSecret">Secret:</label>
                    <input type="password" class="form-control" id="codeRepoSecret" placeholder="请输入Secret">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('codeRepoModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleSaveCodeRepo()">前往授权</button>
            </div>
        </div>
    </div>
</div>


<div id="editProjectModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑项目信息</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('editProjectModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="editProjectName">项目名称:</label>
                    <input type="text" class="form-control" id="editProjectName" value="My Awesome Project">
                </div>
                <div class="form-group">
                    <label for="editProjectType">项目类型:</label>
                    <select class="form-control" id="editProjectType">
                        <option value="k8s" selected>Kubernetes (K8s)</option>
                        <option value="helm">Helm Chart</option>
                        <option value="vm">虚拟机</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editProjectDescription">项目描述:</label>
                    <textarea class="form-control" id="editProjectDescription" rows="3">这是一个用于演示目的的K8s示例项目，包含多个微服务。</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('editProjectModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleEditProject()">保存更改</button>
            </div>
        </div>
    </div>
</div>

<div id="newEnvironmentModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="environmentModalTitle">新建环境</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('newEnvironmentModal')">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="envConfigureModeName">
                <div class="form-group">
                    <label for="envName">环境名称:</label>
                    <input type="text" class="form-control" id="envName" placeholder="例如: staging, uat-blue">
                </div>
                <div class="form-group">
                    <label>环境类型:</label>
                    <div style="margin-top: 8px;">
                        <label style="margin-right: 20px; font-weight: normal;">
                            <input type="radio" name="envType" value="test" checked style="margin-right: 5px;">
                            测试环境
                        </label>
                        <label style="font-weight: normal;">
                            <input type="radio" name="envType" value="production" style="margin-right: 5px;">
                            生产环境
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="envClusterSelect">选择K8s集群:</label>
                    <select class="form-control" id="envClusterSelect">
                        <option value="dev-cluster-01">dev-cluster-01 (华东)</option>
                        <option value="qa-cluster-ap-south">qa-cluster-ap-south (亚太南)</option>
                        <option value="prod-cluster-us-east">prod-cluster-us-east (美东)</option>
                        <option value="shared-cluster-01">shared-cluster-01 (共享)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="envImageRegistry">镜像仓库:</label>
                    <select class="form-control" id="envImageRegistry">
                        <option value="harbor.company.com">harbor.company.com (企业私有仓库)</option>
                        <option value="registry.cn-hangzhou.aliyuncs.com">registry.cn-hangzhou.aliyuncs.com (阿里云镜像仓库)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="envNamespace">K8s命名空间:</label>
                    <input type="text" class="form-control" id="envNamespace" placeholder="默认: [项目名]-[环境名]">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('newEnvironmentModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleSaveEnvironment()">保存环境</button>
            </div>
        </div>
    </div>
</div>

<div id="environmentHistoryModal" class="modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">环境历史版本: <span id="envHistoryName"></span></h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('environmentHistoryModal')">&times;</button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <div class="env-history-controls">
                    <button class="btn btn-info btn-sm" id="compareEnvVersionsBtn" onclick="toggleEnvVersionCompareMode(this)">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right:4px;"><path d="M0 5h16v1H0V5zm8 6h8v1H8v-1zM0 11h6v1H0v-1zM0 2h16v1H0V2zm8 3h8v1H8V5zM0 8h6v1H0V8z"/></svg>
                        对比版本
                    </button>
                    <button class="btn btn-primary btn-sm" id="viewVersionDiffBtn" style="display:none;" onclick="handleCompareEnvVersions()">查看选中版本Diff</button>
                </div>
                <p style="font-size: 13px; color: #718096; margin-bottom: 15px;">查看此环境的部署历史记录，并可选择回滚到特定版本。</p>
                <table class="data-table env-history-table">
                    <thead>
                    <tr>
                        <th style="width: 40px;">选择</th>
                        <th>版本ID / 发布ID</th>
                        <th>发布时间</th>
                        <th>触发者</th>
                        <th>包含的服务版本 (摘要)</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="envHistoryTableBody">
                    <tr>
                        <td><input type="checkbox" class="env-version-checkbox" name="envVersionSelect" value="release-20250605-1430" style="display:none;"></td>
                        <td>release-20250605-1430</td>
                        <td>2025-06-05 14:30</td>
                        <td>张三</td>
                        <td>frontend:v1.2.3, backend:v1.1.0</td>
                        <td class="table-actions">
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('查看版本 release-20250605-1430 的详细配置 (YAML/JSON)...')">查看配置</button>
                            <button class="btn btn-warning btn-sm" onclick="customAlert('回滚到版本 release-20250605-1430 (功能待实现)')">回滚</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="env-version-checkbox" name="envVersionSelect" value="release-20250603-1000" style="display:none;"></td>
                        <td>release-20250603-1000</td>
                        <td>2025-06-03 10:00</td>
                        <td>李四 (自动触发)</td>
                        <td>frontend:v1.2.2, backend:v1.0.5</td>
                        <td class="table-actions">
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('查看版本 release-20250603-1000 的详细配置...')">查看配置</button>
                            <button class="btn btn-warning btn-sm" onclick="customAlert('回滚到版本 release-20250603-1000 (功能待实现)')">回滚</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="env-version-checkbox" name="envVersionSelect" value="release-20250528-1700" style="display:none;"></td>
                        <td>release-20250528-1700</td>
                        <td>2025-05-28 17:00</td>
                        <td>王五</td>
                        <td>frontend:v1.2.0, backend:v1.0.0</td>
                        <td class="table-actions">
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('查看版本 release-20250528-1700 的详细配置...')">查看配置</button>
                            <button class="btn btn-warning btn-sm" onclick="customAlert('回滚到版本 release-20250528-1700 (功能待实现)')">回滚</button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('environmentHistoryModal')">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Service Modal -->
<div id="serviceModal" class="modal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceModalTitle">添加服务</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('serviceModal')">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="serviceEditMode">
                <div class="form-group">
                    <label for="serviceName">服务名称:</label>
                    <input type="text" class="form-control" id="serviceName" placeholder="请输入服务名称">
                </div>
                <div class="form-group">
                    <label>来源:</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="sourceCode" name="serviceSource" value="code" checked>
                            <label for="sourceCode">代码源</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="sourceManual" name="serviceSource" value="manual">
                            <label for="sourceManual">手动输入</label>
                        </div>
                    </div>
                </div>
                <div id="importFromCodeSection">
                    <button type="button" class="btn btn-info btn-sm import-from-code-btn" onclick="openModal('importFromCodeModal')">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                        </svg>
                        从代码源导入
                    </button>
                </div>
                <div class="service-editor-container">
                    <div class="service-editor-left">
                        <label style="margin-bottom: 8px; font-weight: 500;">服务配置文件:</label>
                        <textarea class="code-editor" id="serviceContent" placeholder="请输入或从代码源导入服务配置文件内容..."></textarea>
                        <button type="button" class="btn btn-info btn-sm" onclick="parseServiceConfig()" style="margin-top: 10px;">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                                <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                                <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
                            </svg>
                            解析配置
                        </button>
                    </div>
                    <div class="service-editor-right">
                        <label style="margin-bottom: 8px; font-weight: 500;">服务变量配置:</label>
                        <div class="service-variables-container" id="serviceVariables">
                            <!-- 服务组件区域 -->
                            <div class="variable-group">
                                <h6>服务组件</h6>
                                <div style="background: #f8f9fa; border-radius: 6px; padding: 15px; margin-bottom: 15px;">
                                    <div style="margin-bottom: 10px; font-size: 14px; font-weight: 500; color: #4a5568;">镜像列表</div>
                                    <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
                                        <thead>
                                            <tr style="background: #e2e8f0;">
                                                <th style="padding: 8px 10px; text-align: left; border: 1px solid #cbd5e0; font-weight: 600; color: #2d3748;">服务组件</th>
                                                <th style="padding: 8px 10px; text-align: left; border: 1px solid #cbd5e0; font-weight: 600; color: #2d3748;">镜像名</th>
                                                <th style="padding: 8px 10px; text-align: left; border: 1px solid #cbd5e0; font-weight: 600; color: #2d3748;">当前版本</th>
                                            </tr>
                                        </thead>
                                        <tbody id="serviceComponentsList">
                                            <tr>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">frontend</td>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">nginx</td>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">
                                                    <select style="width: 100%; padding: 4px 6px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 12px;">
                                                        <option value="latest">latest</option>
                                                        <option value="1.21">1.21</option>
                                                        <option value="1.20">1.20</option>
                                                        <option value="alpine">alpine</option>
                                                    </select>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">backend</td>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">golang</td>
                                                <td style="padding: 8px 10px; border: 1px solid #cbd5e0; background: white;">
                                                    <select style="width: 100%; padding: 4px 6px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 12px;">
                                                        <option value="1.19">1.19</option>
                                                        <option value="1.18">1.18</option>
                                                        <option value="alpine">alpine</option>
                                                    </select>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 自定义变量区域 -->
                            <div class="variable-group">
                                <h6>自定义变量</h6>
                                <div class="variable-item">
                                    <label>数据库URL:</label>
                                    <input type="text" value="postgresql://localhost:5432/mydb" placeholder="数据库连接URL">
                                </div>
                                <div class="variable-item">
                                    <label>Redis URL:</label>
                                    <input type="text" value="redis://localhost:6379" placeholder="Redis连接URL">
                                </div>
                                <div class="variable-item">
                                    <label>日志级别:</label>
                                    <input type="text" value="INFO" placeholder="DEBUG/INFO/WARN/ERROR">
                                </div>
                                <div class="variable-item">
                                    <label>副本数:</label>
                                    <input type="number" value="3" placeholder="副本数">
                                </div>
                                <div class="variable-item">
                                    <label>CPU限制:</label>
                                    <input type="text" value="500m" placeholder="CPU限制">
                                </div>
                                <div class="variable-item">
                                    <label>内存限制:</label>
                                    <input type="text" value="512Mi" placeholder="内存限制">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('serviceModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleSaveService()">保存服务</button>
            </div>
        </div>
    </div>
</div>

<!-- Import from Code Modal -->
<div id="importFromCodeModal" class="modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建服务 - 从代码库同步</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('importFromCodeModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="importCodeSource">代码源:</label>
                    <select class="form-control" id="importCodeSource">
                        <option value="">请选择代码源</option>
                        <option value="my-gitlab">my-gitlab (https://gitlab.example.com)</option>
                        <option value="corp-github">corp-github (https://github.com/corp-org)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="importOrgUser">组织名/用户名:</label>
                    <select class="form-control" id="importOrgUser">
                        <option value="">请选择组织名/用户名</option>
                        <option value="my-org">my-org</option>
                        <option value="dev-team">dev-team</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="importRepository">代码库:</label>
                    <select class="form-control" id="importRepository">
                        <option value="">请选择代码库</option>
                        <option value="frontend-app">frontend-app</option>
                        <option value="backend-api">backend-api</option>
                        <option value="microservice-demo">microservice-demo</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="importBranch">分支:</label>
                    <select class="form-control" id="importBranch">
                        <option value="">请选择分支</option>
                        <option value="main">main</option>
                        <option value="develop">develop</option>
                        <option value="feature/k8s-config">feature/k8s-config</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="importFilePath">选择文件(夹):</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="text" class="form-control" id="importFilePath" placeholder="例如: k8s/deployment.yaml" style="flex: 1;">
                        <button type="button" class="btn btn-info btn-sm">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('importFromCodeModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleImportFromCode()">同步</button>
            </div>
        </div>
    </div>
</div>



<!-- Environment Detail Modal -->
<div id="environmentDetailModal" class="modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">环境详情: <span id="envDetailName"></span></h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('environmentDetailModal')">&times;</button>
            </div>
            <div class="modal-body">
                <table class="data-table">
                    <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态(副本)</th>
                        <th>镜像</th>
                        <th>外部访问</th>
                        <th style="width: 350px; min-width: 350px;">操作</th>
                    </tr>
                    </thead>
                    <tbody id="envDetailServicesList">
                    <tr>
                        <td>
                            frontend
                        </td>
                        <td>
                            <span style="color: green;">Running(2)</span>
                        </td>
                        <td>frontend:latest</td>
                        <td>Ingress: N/A</td>
                        <td class="table-actions" style="white-space: nowrap;">
                            <button class="btn btn-info btn-sm" onclick="openModal('serviceDetailModal', 'frontend')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">详情</button>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('重启 frontend 服务')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">重启</button>
                            <button class="btn btn-info btn-sm" onclick="customAlert('更新 frontend 服务')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">更新</button>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 frontend 历史版本')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">历史版本</button>
                            <button class="btn btn-danger btn-sm" onclick="handleDeleteService('frontend')" style="padding: 2px 6px; font-size: 11px;">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            backend
                        </td>
                        <td>
                            <span style="color: green;">Running(3)</span>
                        </td>
                        <td>backend:latest</td>
                        <td>Ingress: N/A</td>
                        <td class="table-actions" style="white-space: nowrap;">
                            <button class="btn btn-info btn-sm" onclick="openModal('serviceDetailModal', 'backend')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">详情</button>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('重启 backend 服务')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">重启</button>
                            <button class="btn btn-info btn-sm" onclick="customAlert('更新 backend 服务')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">更新</button>
                            <button class="btn btn-secondary btn-sm" onclick="customAlert('查看 backend 历史版本')" style="padding: 2px 6px; font-size: 11px; margin-right: 2px;">历史版本</button>
                            <button class="btn btn-danger btn-sm" onclick="handleDeleteService('backend')" style="padding: 2px 6px; font-size: 11px;">删除</button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Service Detail Modal -->
<div id="serviceDetailModal" class="modal">
    <div class="modal-dialog modal-xl" style="max-width: 1200px;">
        <div class="modal-content" style="height: 90vh;">
            <div class="modal-header">
                <h5 class="modal-title">服务详情: <span id="serviceDetailName"></span></h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('serviceDetailModal')">&times;</button>
            </div>
            <div class="modal-body" style="height: calc(90vh - 120px); overflow-y: auto; padding: 20px;">

                <!-- 服务基本信息 -->
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h6 style="margin: 0; color: #2d3748; font-weight: 600;">服务实例</h6>
                        <button class="btn btn-primary btn-sm" onclick="customAlert('刷新服务实例信息')">
                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                                <path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                            </svg>
                            刷新
                        </button>
                    </div>

                    <table class="data-table" style="margin: 0;">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>镜像</th>
                                <th>副本数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td id="serviceInstanceName">redis</td>
                                <td>
                                    <span style="color: #2d3748; font-weight: 500;">redis:alpine</span>
                                </td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('减少副本数量')" style="padding: 2px 6px; font-size: 12px; line-height: 1;">-</button>
                                        <span style="color: #4a5568; font-weight: 500; min-width: 20px; text-align: center;">2</span>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('增加副本数量')" style="padding: 2px 6px; font-size: 12px; line-height: 1;">+</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pods 列表 -->
                <div>
                    <h6 style="margin-bottom: 15px; color: #2d3748; font-weight: 600;">Pods</h6>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <!-- Pod 1 -->
                        <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                            <!-- Pod Header -->
                            <div style="background: #f7fafc; padding: 12px 15px; border-bottom: 1px solid #e2e8f0;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="font-weight: 500; color: #2d3748;">redis-6b557c6f96-6x3fz</span>
                                        <span style="background: #c6f6d5; color: #22543d; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Running</span>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('全屏查看 Pod')">全屏查看</button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="customAlert('重启 Pod')">重启</button>
                                    </div>
                                </div>
                                <!-- Pod基本信息 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 10px; font-size: 13px;">
                                    <div>
                                        <span style="color: #4a5568;">实例IP:</span>
                                        <span style="color: #2d3748; font-weight: 500; margin-left: 8px;">*************</span>
                                    </div>
                                    <div>
                                        <span style="color: #4a5568;">节点名称:</span>
                                        <span style="color: #2d3748; margin-left: 8px;">**********(mgr test 09)</span>
                                    </div>
                                    <div>
                                        <span style="color: #4a5568;">启动时间:</span>
                                        <span style="color: #2d3748; margin-left: 8px;">2025-06-04 16:09:47</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 容器列表 -->
                            <div style="padding: 15px;">
                                <h6 style="margin-bottom: 10px; color: #4a5568; font-size: 14px; font-weight: 600;">容器 (1)</h6>

                                <!-- 容器 1 -->
                                <div style="border: 1px solid #e2e8f0; border-radius: 6px; margin-bottom: 10px;">
                                    <div style="background: #fafafa; padding: 10px 12px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span style="font-weight: 500; color: #2d3748;">redis</span>
                                            <span style="background: #c6f6d5; color: #22543d; padding: 1px 6px; border-radius: 10px; font-size: 11px; font-weight: 500;">Running</span>
                                        </div>
                                        <div style="display: flex; gap: 6px;">
                                            <button class="btn btn-outline-info btn-sm" onclick="customAlert('查看 redis 容器日志')" style="padding: 2px 8px; font-size: 11px;">查看日志</button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('启动 redis 容器调试')" style="padding: 2px 8px; font-size: 11px;">启动调试</button>
                                        </div>
                                    </div>
                                    <div style="padding: 10px 12px;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 13px;">
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">镜像:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">redis:alpine</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">状态:</span>
                                                    <span style="color: #38c172; margin-left: 8px; font-weight: 500;">running</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">端口:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">6379/TCP</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">重启次数:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pod 2 (多容器示例) -->
                        <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                            <!-- Pod Header -->
                            <div style="background: #f7fafc; padding: 12px 15px; border-bottom: 1px solid #e2e8f0;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="font-weight: 500; color: #2d3748;">frontend-7d4b8c9f5d-k8m2n</span>
                                        <span style="background: #c6f6d5; color: #22543d; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Running</span>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('全屏查看 Pod')">全屏查看</button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="customAlert('重启 Pod')">重启</button>
                                    </div>
                                </div>
                                <!-- Pod基本信息 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 10px; font-size: 13px;">
                                    <div>
                                        <span style="color: #4a5568;">实例IP:</span>
                                        <span style="color: #2d3748; font-weight: 500; margin-left: 8px;">*************</span>
                                    </div>
                                    <div>
                                        <span style="color: #4a5568;">节点名称:</span>
                                        <span style="color: #2d3748; margin-left: 8px;">***********(mgr test 10)</span>
                                    </div>
                                    <div>
                                        <span style="color: #4a5568;">启动时间:</span>
                                        <span style="color: #2d3748; margin-left: 8px;">2025-06-04 16:08:22</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 容器列表 -->
                            <div style="padding: 15px;">
                                <h6 style="margin-bottom: 10px; color: #4a5568; font-size: 14px; font-weight: 600;">容器 (2)</h6>

                                <!-- 容器 1: frontend -->
                                <div style="border: 1px solid #e2e8f0; border-radius: 6px; margin-bottom: 10px;">
                                    <div style="background: #fafafa; padding: 10px 12px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span style="font-weight: 500; color: #2d3748;">frontend</span>
                                            <span style="background: #c6f6d5; color: #22543d; padding: 1px 6px; border-radius: 10px; font-size: 11px; font-weight: 500;">Running</span>
                                        </div>
                                        <div style="display: flex; gap: 6px;">
                                            <button class="btn btn-outline-info btn-sm" onclick="customAlert('查看 frontend 容器日志')" style="padding: 2px 8px; font-size: 11px;">查看日志</button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('启动 frontend 容器调试')" style="padding: 2px 8px; font-size: 11px;">启动调试</button>
                                        </div>
                                    </div>
                                    <div style="padding: 10px 12px;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 13px;">
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">镜像:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">frontend:v2.1.0</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">状态:</span>
                                                    <span style="color: #38c172; margin-left: 8px; font-weight: 500;">running</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">端口:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">80/TCP</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">重启次数:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 容器 2: sidecar -->
                                <div style="border: 1px solid #e2e8f0; border-radius: 6px; margin-bottom: 10px;">
                                    <div style="background: #fafafa; padding: 10px 12px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span style="font-weight: 500; color: #2d3748;">sidecar-proxy</span>
                                            <span style="background: #c6f6d5; color: #22543d; padding: 1px 6px; border-radius: 10px; font-size: 11px; font-weight: 500;">Running</span>
                                        </div>
                                        <div style="display: flex; gap: 6px;">
                                            <button class="btn btn-outline-info btn-sm" onclick="customAlert('查看 sidecar-proxy 容器日志')" style="padding: 2px 8px; font-size: 11px;">查看日志</button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="customAlert('启动 sidecar-proxy 容器调试')" style="padding: 2px 8px; font-size: 11px;">启动调试</button>
                                        </div>
                                    </div>
                                    <div style="padding: 10px 12px;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 13px;">
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">镜像:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">envoy:v1.18.3</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">状态:</span>
                                                    <span style="color: #38c172; margin-left: 8px; font-weight: 500;">running</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">端口:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">15001/TCP</span>
                                                </div>
                                                <div style="margin-bottom: 8px;">
                                                    <span style="color: #4a5568;">重启次数:</span>
                                                    <span style="color: #2d3748; margin-left: 8px;">1</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('serviceDetailModal')">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Release Detail Modal -->
<div id="releaseDetailModal" class="modal">
    <div class="modal-dialog modal-xl" style="max-width: 1200px;">
        <div class="modal-content" style="height: 90vh;">
            <div class="modal-header">
                <h5 class="modal-title">发布详情: <span id="releaseDetailTitle"></span></h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('releaseDetailModal')">&times;</button>
            </div>
            <div class="modal-body" style="height: calc(90vh - 120px); overflow: hidden; padding: 0;">

                <!-- Tab Navigation -->
                <div style="border-bottom: 1px solid #e2e8f0; background: #f8f9fa;">
                    <div style="display: flex; padding: 0 20px;">
                        <button class="release-detail-tab active" onclick="switchReleaseDetailTab(event, 'yamlTab')" style="padding: 12px 20px; border: none; background: none; color: #4a5568; font-weight: 500; border-bottom: 2px solid transparent; cursor: pointer;">
                            YAML
                        </button>
                        <button class="release-detail-tab" onclick="switchReleaseDetailTab(event, 'variablesTab')" style="padding: 12px 20px; border: none; background: none; color: #4a5568; font-weight: 500; border-bottom: 2px solid transparent; cursor: pointer;">
                            变量
                        </button>
                    </div>
                </div>

                <!-- YAML Tab Content -->
                <div id="yamlTab" class="release-detail-tab-content active" style="height: calc(100% - 60px); padding: 20px; overflow-y: auto;">
                    <div style="background: #f8f9fa; border: 1px solid #e2e8f0; border-radius: 6px; height: 100%; position: relative;">
                        <div style="position: absolute; top: 10px; right: 10px; z-index: 10;">
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyYamlContent()" style="padding: 4px 8px; font-size: 12px;">
                                <svg width="12" height="12" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 4px;">
                                    <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                    <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                </svg>
                                复制
                            </button>
                        </div>
                        <pre id="yamlContent" style="margin: 0; padding: 15px; height: 100%; overflow: auto; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.4; background: transparent; border: none; color: #2d3748;"><code>apiVersion: v1
kind: Namespace
metadata:
  name: devops-site
---
apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  name: my-html-deployment
  namespace: devops-site
spec:
  replicas: 1
  selector:
    matchLabels:
      app: my-html-app
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: my-html-app
    spec:
      containers:
      - image: nginx:latest
        name: nginx
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        env:
        - name: VERSION
          value: "v2.1.0"
        - name: ENVIRONMENT
          value: "production"
status: {}
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  name: my-html-service
  namespace: devops-site
spec:
  ports:
  - port: 80
    protocol: TCP
    targetPort: 80
  selector:
    app: my-html-app
  type: ClusterIP
status:
  loadBalancer: {}</code></pre>
                    </div>
                </div>

                <!-- Variables Tab Content -->
                <div id="variablesTab" class="release-detail-tab-content" style="height: calc(100% - 60px); padding: 20px; overflow-y: auto; display: none;">
                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 6px; height: 100%;">
                        <div style="padding: 20px;">
                            <h6 style="margin-bottom: 15px; color: #2d3748; font-weight: 600;">环境变量</h6>
                            <table class="data-table" style="margin-bottom: 30px;">
                                <thead>
                                    <tr>
                                        <th>变量名</th>
                                        <th>变量值</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>VERSION</code></td>
                                        <td><code>v2.1.0</code></td>
                                        <td>应用版本号</td>
                                    </tr>
                                    <tr>
                                        <td><code>ENVIRONMENT</code></td>
                                        <td><code>production</code></td>
                                        <td>部署环境标识</td>
                                    </tr>
                                    <tr>
                                        <td><code>DATABASE_URL</code></td>
                                        <td><code>mysql://db:3306/myapp</code></td>
                                        <td>数据库连接地址</td>
                                    </tr>
                                    <tr>
                                        <td><code>REDIS_URL</code></td>
                                        <td><code>redis://cache:6379</code></td>
                                        <td>Redis缓存地址</td>
                                    </tr>
                                    <tr>
                                        <td><code>LOG_LEVEL</code></td>
                                        <td><code>info</code></td>
                                        <td>日志级别</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h6 style="margin-bottom: 15px; color: #2d3748; font-weight: 600;">配置参数</h6>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>参数值</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>replicas</code></td>
                                        <td><code>1</code></td>
                                        <td>副本数量</td>
                                    </tr>
                                    <tr>
                                        <td><code>cpu.limits</code></td>
                                        <td><code>500m</code></td>
                                        <td>CPU限制</td>
                                    </tr>
                                    <tr>
                                        <td><code>memory.limits</code></td>
                                        <td><code>512Mi</code></td>
                                        <td>内存限制</td>
                                    </tr>
                                    <tr>
                                        <td><code>cpu.requests</code></td>
                                        <td><code>100m</code></td>
                                        <td>CPU请求</td>
                                    </tr>
                                    <tr>
                                        <td><code>memory.requests</code></td>
                                        <td><code>128Mi</code></td>
                                        <td>内存请求</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('releaseDetailModal')">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Deploy to Environment Modal -->
<div id="deployToEnvironmentModal" class="modal">
    <div class="modal-dialog modal-xl" style="max-width:900px;">
        <div class="modal-content" style="height:90vh;">
            <div class="modal-header">
                <h5 class="modal-title">服务发布</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('deployToEnvironmentModal')">&times;</button>
            </div>
            <div class="modal-body" style="height:calc(90vh - 120px); overflow-y: auto;">
                <div class="release-form-container">
                    <div class="release-form-group">
                        <label class="release-form-label">发布环境 *</label>
                        <input type="text" class="release-form-input" id="deployEnvName" readonly>
                    </div>

                    <div class="release-form-group">
                        <label class="release-form-label">发布说明</label>
                        <textarea class="release-form-textarea" id="deployDescription" placeholder="请输入本次发布的说明信息..."></textarea>
                    </div>

                    <div class="release-form-group">
                        <label class="release-form-label">选择服务 *</label>
                        <div class="select-services-wrapper" onclick="toggleServicesDropdown()">
                            <div class="selected-tags" id="selectedTags">
                                <span style="color: #999; font-size: 14px;">请选择要发布的服务</span>
                            </div>
                            <svg class="dropdown-arrow" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M7 10l5 5 5-5H7z"/>
                            </svg>
                            <div class="services-dropdown" id="servicesDropdown">
                                <div class="service-option">
                                    <input type="checkbox" id="service-backend" value="backend" onchange="toggleService(this)">
                                    <label for="service-backend">backend</label>
                                </div>
                                <div class="service-option">
                                    <input type="checkbox" id="service-frontend" value="frontend" onchange="toggleService(this)">
                                    <label for="service-frontend">frontend</label>
                                </div>
                                <div class="service-option">
                                    <input type="checkbox" id="service-database" value="database" onchange="toggleService(this)">
                                    <label for="service-database">database</label>
                                </div>
                                <div class="service-option">
                                    <input type="checkbox" id="service-cache" value="cache" onchange="toggleService(this)">
                                    <label for="service-cache">cache</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backend Service Card -->
                    <div class="service-card hidden" id="backend-card" data-service="backend">
                        <button class="service-card-close" onclick="removeService('backend')">×</button>
                        <div class="service-card-title">backend</div>

                        <div class="image-selection-title">镜像选择</div>
                        <select class="image-select" id="backend-image">
                            <option value="backend:latest">backend:latest (最新版本)</option>
                            <option value="backend:v1.2.3">backend:v1.2.3 (稳定版本)</option>
                            <option value="backend:v1.2.2">backend:v1.2.2 (上一版本)</option>
                            <option value="backend:dev-20241201">backend:dev-20241201 (开发版本)</option>
                        </select>

                        <div class="resource-detection-title">资源探测</div>
                        <div class="resource-list">
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Deployment/backend-api
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                ConfigMap/backend-config
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Service/backend-service
                            </div>
                        </div>
                        <div class="variable-config-title">变量配置</div>
                        <div class="variable-list">
                            <div class="variable-item">
                                <label class="variable-label">VERSION <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="v1.23">
                            </div>
                            <div class="variable-item">
                                <label class="variable-label">MYSQL <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="10.218.34.12">
                            </div>
                        </div>
                    </div>

                    <!-- Frontend Service Card -->
                    <div class="service-card hidden" id="frontend-card" data-service="frontend">
                        <button class="service-card-close" onclick="removeService('frontend')">×</button>
                        <div class="service-card-title">frontend</div>

                        <div class="image-selection-title">镜像选择</div>
                        <select class="image-select" id="frontend-image">
                            <option value="frontend:latest">frontend:latest (最新版本)</option>
                            <option value="frontend:v2.1.0">frontend:v2.1.0 (稳定版本)</option>
                            <option value="frontend:v2.0.5">frontend:v2.0.5 (上一版本)</option>
                            <option value="frontend:dev-20241201">frontend:dev-20241201 (开发版本)</option>
                        </select>

                        <div class="resource-detection-title">资源探测</div>
                        <div class="resource-list">
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Deployment/frontend-web
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                ConfigMap/frontend-config
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Service/frontend-service
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Ingress/frontend-ingress
                            </div>
                        </div>
                        <div class="variable-config-title">变量配置</div>
                        <div class="variable-list">
                            <div class="variable-item">
                                <label class="variable-label">VERSION <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="v2.1.0">
                            </div>
                            <div class="variable-item">
                                <label class="variable-label">HOST <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="op.scitix.ai">
                            </div>
                        </div>
                    </div>

                    <!-- Database Service Card -->
                    <div class="service-card hidden" id="database-card" data-service="database">
                        <button class="service-card-close" onclick="removeService('database')">×</button>
                        <div class="service-card-title">database</div>

                        <div class="image-selection-title">镜像选择</div>
                        <select class="image-select" id="database-image">
                            <option value="mysql:8.0">mysql:8.0 (推荐版本)</option>
                            <option value="mysql:5.7">mysql:5.7 (兼容版本)</option>
                            <option value="postgres:14">postgres:14 (PostgreSQL)</option>
                            <option value="redis:7.0">redis:7.0 (Redis缓存)</option>
                        </select>

                        <div class="resource-detection-title">资源探测</div>
                        <div class="resource-list">
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                StatefulSet/database
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Service/database-service
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                PersistentVolumeClaim/database-pvc
                            </div>
                        </div>
                        <div class="variable-config-title">变量配置</div>
                        <div class="variable-list">
                            <div class="variable-item">
                                <label class="variable-label">DB_NAME <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="myapp_db">
                            </div>
                            <div class="variable-item">
                                <label class="variable-label">DB_USER <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="app_user">
                            </div>
                        </div>
                    </div>

                    <!-- Cache Service Card -->
                    <div class="service-card hidden" id="cache-card" data-service="cache">
                        <button class="service-card-close" onclick="removeService('cache')">×</button>
                        <div class="service-card-title">cache</div>

                        <div class="image-selection-title">镜像选择</div>
                        <select class="image-select" id="cache-image">
                            <option value="redis:7.0">redis:7.0 (最新稳定版)</option>
                            <option value="redis:6.2">redis:6.2 (LTS版本)</option>
                            <option value="memcached:1.6">memcached:1.6 (Memcached)</option>
                            <option value="redis:7.0-alpine">redis:7.0-alpine (轻量版)</option>
                        </select>

                        <div class="resource-detection-title">资源探测</div>
                        <div class="resource-list">
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Deployment/cache-redis
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                Service/cache-service
                            </div>
                            <div class="resource-item">
                                <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                                ConfigMap/cache-config
                            </div>
                        </div>
                        <div class="variable-config-title">变量配置</div>
                        <div class="variable-list">
                            <div class="variable-item">
                                <label class="variable-label">REDIS_PORT <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="6379">
                            </div>
                            <div class="variable-item">
                                <label class="variable-label">MAX_MEMORY <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                                <input type="text" class="variable-input-field" value="256mb">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('deployToEnvironmentModal')">取消</button>
                <button type="button" class="btn btn-info" onclick="previewDeployChanges()">预览变更</button>
                <button type="button" class="btn btn-primary" onclick="confirmDeploy()">确认发布</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Changes Modal -->
<div id="previewChangesModal" class="modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">预览变更</h5>
                <button type="button" class="modal-close-btn" onclick="closeModal('previewChangesModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 20px; height: 400px;">
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h6 style="margin-bottom: 10px; color: #dc3545;">变更前</h6>
                        <textarea readonly style="flex: 1; font-family: monospace; font-size: 12px; border: 1px solid #e2e8f0; border-radius: 4px; padding: 10px; background: #f8f9fa;">
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
spec:
  replicas: 2
  template:
    spec:
      containers:
      - name: frontend
        image: frontend:v1.0.0
        env:
        - name: VERSION
          value: "v1.0.0"
                        </textarea>
                    </div>
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h6 style="margin-bottom: 10px; color: #28a745;">变更后</h6>
                        <textarea readonly style="flex: 1; font-family: monospace; font-size: 12px; border: 1px solid #e2e8f0; border-radius: 4px; padding: 10px; background: #f8f9fa;">
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: frontend
        image: frontend:test
        env:
        - name: VERSION
          value: "test"
                        </textarea>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #f1f5f9; border-radius: 6px;">
                    <h6 style="margin-bottom: 8px; color: #667eea;">变更摘要:</h6>
                    <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                        <li>副本数: 2 → 3</li>
                        <li>镜像版本: frontend:v1.0.0 → frontend:test</li>
                        <li>环境变量 VERSION: "v1.0.0" → "test"</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('previewChangesModal')">关闭</button>
            </div>
        </div>
    </div>
</div>


<script>
    function openTab(evt, tabName) {
        var i, tabcontent, tablinks;
        tabcontent = document.getElementsByClassName("tab-pane");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
            tabcontent[i].classList.remove("active");
        }
        tablinks = document.getElementsByClassName("tab-button");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].classList.remove("active");
        }

        const activeTabContent = document.getElementById(tabName);
        if (activeTabContent) {
            activeTabContent.style.display = "block";
            activeTabContent.classList.add("active");
        }

        if (evt && evt.currentTarget) {
            evt.currentTarget.classList.add("active");
        }
    }

    function openCompileBuildSubTab(evt, subTabName) {
        var i, subTabContent, subTabLinks;
        var parentTabPane = document.getElementById('compileBuild');
        subTabContent = parentTabPane.getElementsByClassName("sub-tab-pane");
        for (i = 0; i < subTabContent.length; i++) {
            subTabContent[i].style.display = "none";
            subTabContent[i].classList.remove("active");
        }

        subTabLinks = parentTabPane.getElementsByClassName("sub-tab-button");
        for (i = 0; i < subTabLinks.length; i++) {
            subTabLinks[i].classList.remove("active");
        }

        const activeSubTabContent = document.getElementById(subTabName);
        if (activeSubTabContent) {
            activeSubTabContent.style.display = "block";
            activeSubTabContent.classList.add("active");
        }

        if (evt && evt.currentTarget) {
            evt.currentTarget.classList.add("active");
        }
    }

    function openProjectSettingsSubTab(evt, subTabName) {
        var i, subTabContent, subTabLinks;
        var parentTabPane = document.getElementById('projectSettings');
        subTabContent = parentTabPane.getElementsByClassName("sub-tab-pane");
        for (i = 0; i < subTabContent.length; i++) {
            subTabContent[i].style.display = "none";
            subTabContent[i].classList.remove("active");
        }

        subTabLinks = parentTabPane.getElementsByClassName("sub-tab-button");
        for (i = 0; i < subTabLinks.length; i++) {
            subTabLinks[i].classList.remove("active");
        }

        const activeSubTabContent = document.getElementById(subTabName);
        if (activeSubTabContent) {
            activeSubTabContent.style.display = "block";
            activeSubTabContent.classList.add("active");
        }

        if (evt && evt.currentTarget) {
            evt.currentTarget.classList.add("active");
        }
    }

    function navigateTo(page) {
        window.location.href = page;
    }

    function customAlert(message) {
        const modal = document.getElementById('customAlertModal');
        const messageP = document.getElementById('customAlertMessage');
        if(modal && messageP) {
            messageP.textContent = message;
            modal.style.display = 'flex';
        } else {
            alert(message);
        }
    }
    function closeCustomAlert() {
        document.getElementById('customAlertModal').style.display = 'none';
    }

    const userProfileToggle = document.getElementById('userProfileDropdownToggle');
    const userProfileDropdown = document.getElementById('userProfileDropdown');

    if(userProfileToggle && userProfileDropdown) {
        userProfileToggle.addEventListener('click', function(event) {
            event.stopPropagation();
            userProfileDropdown.classList.toggle('show');
        });

        document.addEventListener('click', function(event) {
            if (userProfileDropdown.classList.contains('show') && !userProfileToggle.contains(event.target)) {
                userProfileDropdown.classList.remove('show');
            }
        });
    }

    function openModal(modalId, modeOrData, dataObject) {
        const modal = document.getElementById(modalId);
        if (modal) {
            // Universal pre-processing for all modals
            // ...

            // Specific logic for each modal
            if (modalId === 'editProjectModal') {
                document.getElementById('editProjectName').value = document.getElementById('projectInfoName').textContent;
                const projectType = document.getElementById('projectInfoType').textContent;
                const typeSelect = document.getElementById('editProjectType');
                for(let i=0; i < typeSelect.options.length; i++){
                    if(typeSelect.options[i].text === projectType){
                        typeSelect.selectedIndex = i;
                        break;
                    }
                }
                document.getElementById('editProjectDescription').value = document.getElementById('projectInfoDescription').textContent;
            } else if (modalId === 'codeRepoModal') {
                const titleEl = document.getElementById('codeRepoModalTitle');
                if(modeOrData === 'add') {
                    titleEl.textContent = '新增代码仓库';
                    // Clear form fields
                    document.getElementById('codeRepoIdentifier').value = '';
                    document.getElementById('codeRepoUrl').value = '';
                    document.getElementById('codeRepoAppId').value = '';
                    document.getElementById('codeRepoSecret').value = '';
                    document.getElementById('codeRepoEditId').value = '';
                } else if (modeOrData === 'edit' && dataObject) {
                    titleEl.textContent = '编辑代码仓库';
                    // Pre-fill form fields
                    document.getElementById('codeRepoIdentifier').value = dataObject.id;
                    document.getElementById('codeRepoUrl').value = dataObject.url;
                    document.getElementById('codeRepoAppId').value = dataObject.appId;
                    document.getElementById('codeRepoSecret').value = dataObject.secret;
                    document.getElementById('codeRepoSource').value = dataObject.source;
                    document.getElementById('codeRepoEditId').value = dataObject.id; // Store original ID for save logic
                }

            } else if (modalId === 'configureEnvironmentModal' && modeOrData) {
                document.getElementById('environmentModalTitle').textContent = '配置环境: ' + modeOrData;
                document.getElementById('envName').value = modeOrData;
                document.getElementById('envConfigureModeName').value = modeOrData;
                document.getElementById('envNamespace').value = `my-awesome-project-${modeOrData.toLowerCase().replace(/\s+/g, '-')}`;
                openModal('newEnvironmentModal');
                return;
            } else if (modalId === 'newEnvironmentModal') {
                document.getElementById('environmentModalTitle').textContent = '新建环境';
                document.getElementById('envName').value = '';
                document.getElementById('envNamespace').value = '';
                document.getElementById('envConfigureModeName').value = '';
                // 重置环境类型为测试环境
                document.querySelector('input[name="envType"][value="test"]').checked = true;
                // 重置集群和镜像仓库选择
                document.getElementById('envClusterSelect').selectedIndex = 0;
                document.getElementById('envImageRegistry').selectedIndex = 0;

            } else if (modalId === 'environmentHistoryModal' && modeOrData) {
                document.getElementById('envHistoryName').textContent = modeOrData;
                resetEnvVersionCompareMode();
            } else if (modalId === 'environmentDetailModal' && modeOrData) {
                document.getElementById('envDetailName').textContent = modeOrData;
            } else if (modalId === 'serviceDetailModal' && modeOrData) {
                document.getElementById('serviceDetailName').textContent = modeOrData;
                document.getElementById('serviceInstanceName').textContent = modeOrData;
            } else if (modalId === 'deployToEnvironmentModal' && modeOrData) {
                document.getElementById('deployEnvName').value = modeOrData;
                // Reset deploy form
                resetDeployForm();
            } else if (modalId === 'serviceModal') {
                const titleEl = document.getElementById('serviceModalTitle');
                if(modeOrData === 'add') {
                    titleEl.textContent = '添加服务';
                    document.getElementById('serviceEditMode').value = 'add';
                    // Clear form fields
                    document.getElementById('serviceName').value = '';
                    document.getElementById('serviceContent').value = '';
                    document.getElementById('sourceCode').checked = true;
                    document.getElementById('sourceManual').checked = false;
                    toggleServiceSourceSection();
                } else if (modeOrData === 'edit') {
                    const serviceName = dataObject; // 第三个参数是服务名称
                    titleEl.textContent = '编辑服务';
                    document.getElementById('serviceEditMode').value = 'edit';

                    // Pre-fill form fields
                    document.getElementById('serviceName').value = serviceName;
                    document.getElementById('sourceCode').checked = false;
                    document.getElementById('sourceManual').checked = true;

                    // 根据服务名称填充不同的YAML内容
                    let yamlContent = '';
                    if (serviceName === 'frontend-service') {
                        yamlContent = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: nginx:latest
        ports:
        - containerPort: 80
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: default
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP`;
                    } else if (serviceName === 'backend-api-service') {
                        yamlContent = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-api-deployment
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend-api
  template:
    metadata:
      labels:
        app: backend-api
    spec:
      containers:
      - name: backend-api
        image: golang:1.19-alpine
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          value: "****************************/myapp"
        - name: REDIS_URL
          value: "redis://cache:6379"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 200m
            memory: 256Mi
---
apiVersion: v1
kind: Service
metadata:
  name: backend-api-service
  namespace: default
spec:
  selector:
    app: backend-api
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP`;
                    } else if (serviceName === 'database-service') {
                        yamlContent = `apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: database-statefulset
  namespace: default
spec:
  serviceName: database-service
  replicas: 1
  selector:
    matchLabels:
      app: database
  template:
    metadata:
      labels:
        app: database
    spec:
      containers:
      - name: postgres
        image: postgres:13
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "myapp"
        - name: POSTGRES_USER
          value: "user"
        - name: POSTGRES_PASSWORD
          value: "password"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 512Mi
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: database-service
  namespace: default
spec:
  selector:
    app: database
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP`;
                    }

                    document.getElementById('serviceContent').value = yamlContent;
                    toggleServiceSourceSection();
                }
            }
            modal.style.display = 'flex';
            modal.onclick = function(event) {
                if (event.target == modal) {
                    closeModal(modalId);
                }
            };
        }
    }

    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            modal.onclick = null;
        }
    }



    function handleEditProject() {
        const newName = document.getElementById('editProjectName').value;
        const newType = document.getElementById('editProjectType').selectedOptions[0].text;
        const newDescription = document.getElementById('editProjectDescription').value;

        document.getElementById('projectInfoName').textContent = newName;
        document.getElementById('projectInfoType').textContent = newType;
        document.getElementById('projectInfoDescription').textContent = newDescription;
        document.querySelector('.header-left h2').textContent = newName;

        customAlert(`项目信息已更新为: 名称=${newName}, 类型=${newType}. (功能待实现)`);
        closeModal('editProjectModal');
    }

    function handleSaveCodeRepo() {
        const id = document.getElementById('codeRepoIdentifier').value;
        const url = document.getElementById('codeRepoUrl').value;
        const originalId = document.getElementById('codeRepoEditId').value;
        const action = originalId ? "更新" : "新增";

        if (!id || !url) {
            customAlert("代码源标识和URL为必填项！");
            return;
        }

        customAlert(`${action}代码仓库 "${id}". (功能待实现)`);
        closeModal('codeRepoModal');
    }

    function handleSaveEnvironment() {
        const envName = document.getElementById('envName').value;
        const envType = document.querySelector('input[name="envType"]:checked').value;
        const cluster = document.getElementById('envClusterSelect').value;
        const imageRegistry = document.getElementById('envImageRegistry').value;
        const namespace = document.getElementById('envNamespace').value;
        const originalName = document.getElementById('envConfigureModeName').value;
        const action = originalName ? "配置" : "新建";

        if (!envName || !cluster || !namespace || !imageRegistry) {
            customAlert("请填写所有必填的环境信息！");
            return;
        }

        const envTypeText = envType === 'test' ? '测试环境' : '生产环境';
        customAlert(`${action}环境 "${envName}" (类型: ${envTypeText}, 集群: ${cluster}, 镜像仓库: ${imageRegistry}, 命名空间: ${namespace}). (功能待实现)`);
        closeModal('newEnvironmentModal');
    }

    function handleDeleteEnvironment(envName) {
        if (confirm(`确定要删除环境 "${envName}" 吗？此操作不可撤销。`)) {
            customAlert(`删除环境 "${envName}". (功能待实现)`);
        }
    }

    function handleDeleteEnvironmentFromDetail() {
        const envName = document.getElementById('envDetailName').textContent;
        if (confirm(`确定要删除环境 "${envName}" 吗？此操作不可撤销。`)) {
            customAlert(`删除环境 "${envName}". (功能待实现)`);
            closeModal('environmentDetailModal');
        }
    }

    function handleDeleteService(serviceName) {
        const envName = document.getElementById('envDetailName').textContent;
        if (confirm(`确定要从环境 "${envName}" 中删除服务 "${serviceName}" 吗？`)) {
            customAlert(`从环境 "${envName}" 删除服务 "${serviceName}". (功能待实现)`);
        }
    }



    // Service Release Form Functions
    let selectedServices = new Set();

    // Toggle services dropdown
    function toggleServicesDropdown() {
        const dropdown = document.getElementById('servicesDropdown');
        const wrapper = document.querySelector('.select-services-wrapper');

        if (dropdown.style.display === 'block') {
            dropdown.style.display = 'none';
            wrapper.classList.remove('focused');
        } else {
            dropdown.style.display = 'block';
            wrapper.classList.add('focused');
        }
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const wrapper = document.querySelector('.select-services-wrapper');
        const dropdown = document.getElementById('servicesDropdown');

        if (wrapper && dropdown && !wrapper.contains(event.target)) {
            dropdown.style.display = 'none';
            wrapper.classList.remove('focused');
        }
    });

    // Toggle service selection
    function toggleService(checkbox) {
        const serviceName = checkbox.value;
        const serviceCard = document.getElementById(serviceName + '-card');

        if (checkbox.checked) {
            selectedServices.add(serviceName);
            if (serviceCard) {
                serviceCard.classList.remove('hidden');
            }
        } else {
            selectedServices.delete(serviceName);
            if (serviceCard) {
                serviceCard.classList.add('hidden');
            }
        }

        updateSelectedTags();
    }

    // Remove service
    function removeService(serviceName) {
        const checkbox = document.getElementById('service-' + serviceName);
        const serviceCard = document.getElementById(serviceName + '-card');

        if (checkbox) {
            checkbox.checked = false;
        }
        if (serviceCard) {
            serviceCard.classList.add('hidden');
        }

        selectedServices.delete(serviceName);
        updateSelectedTags();
    }

    // Update selected tags display
    function updateSelectedTags() {
        const tagsContainer = document.getElementById('selectedTags');

        if (!tagsContainer) return;

        if (selectedServices.size === 0) {
            tagsContainer.innerHTML = '<span style="color: #999; font-size: 14px;">请选择要发布的服务</span>';
        } else {
            tagsContainer.innerHTML = '';
            selectedServices.forEach(service => {
                const tag = document.createElement('span');
                tag.className = 'tag';
                tag.innerHTML = `${service} <span onclick="removeService('${service}')" style="cursor: pointer; margin-left: 4px;">×</span>`;
                tagsContainer.appendChild(tag);
            });
        }
    }

    // Preview deploy changes
    function previewDeployChanges() {
        if (selectedServices.size === 0) {
            customAlert('请先选择要发布的服务');
            return;
        }

        const deployData = collectDeployData();
        console.log('预览变更数据:', deployData);

        // Update preview modal with actual data
        updatePreviewModal(deployData);
        openModal('previewChangesModal');
    }

    // Confirm deploy
    function confirmDeploy() {
        if (selectedServices.size === 0) {
            customAlert('请先选择要发布的服务');
            return;
        }

        const deployData = collectDeployData();
        console.log('确认发布数据:', deployData);

        customAlert(`发布成功！已向环境 "${deployData.environment}" 发布 ${deployData.services.length} 个服务。`);
        closeModal('deployToEnvironmentModal');

        // Reset form
        resetDeployForm();
    }

    // Collect deployment data
    function collectDeployData() {
        const environment = document.getElementById('deployEnvName').value;
        const description = document.getElementById('deployDescription').value;
        const services = [];

        selectedServices.forEach(serviceName => {
            const imageSelect = document.getElementById(serviceName + '-image');
            const variables = {};

            // Collect variables for this service
            const serviceCard = document.getElementById(serviceName + '-card');
            if (serviceCard) {
                const variableInputs = serviceCard.querySelectorAll('.variable-input-field');
                variableInputs.forEach(input => {
                    const label = input.parentElement.querySelector('.variable-label').textContent.trim();
                    variables[label.replace(/\s.*/, '')] = input.value;
                });
            }

            services.push({
                name: serviceName,
                image: imageSelect ? imageSelect.value : '',
                variables: variables
            });
        });

        return {
            environment: environment,
            description: description,
            services: services
        };
    }

    // Reset deploy form
    function resetDeployForm() {
        selectedServices.clear();

        // Reset checkboxes
        document.querySelectorAll('#servicesDropdown input[type="checkbox"]').forEach(cb => {
            cb.checked = false;
        });

        // Hide all service cards
        document.querySelectorAll('.service-card').forEach(card => {
            card.classList.add('hidden');
        });

        // Reset tags display
        updateSelectedTags();

        // Reset description
        const descTextarea = document.getElementById('deployDescription');
        if (descTextarea) {
            descTextarea.value = '';
        }
    }

    // Update preview modal with deploy data
    function updatePreviewModal(deployData) {
        // This function can be enhanced to show actual deployment changes
        // For now, it uses the existing static preview content
    }

    let envVersionCompareModeActive = false;
    let selectedVersionsForCompare = [];

    function toggleEnvVersionCompareMode(button) {
        envVersionCompareModeActive = !envVersionCompareModeActive;
        const checkboxes = document.querySelectorAll('#environmentHistoryModal .env-version-checkbox');
        const viewDiffBtn = document.getElementById('viewVersionDiffBtn');

        checkboxes.forEach(cb => {
            cb.style.display = envVersionCompareModeActive ? 'inline-block' : 'none';
            cb.checked = false;
        });

        if (envVersionCompareModeActive) {
            button.innerHTML = '<svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right:4px;"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/></svg>取消对比';
            button.classList.remove('btn-info');
            button.classList.add('btn-warning');
            viewDiffBtn.style.display = 'none';
            selectedVersionsForCompare = [];
        } else {
            button.innerHTML = '<svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right:4px;"><path d="M0 5h16v1H0V5zm8 6h8v1H8v-1zM0 11h6v1H0v-1zM0 2h16v1H0V2zm8 3h8v1H8V5zM0 8h6v1H0V8z"/></svg>对比版本';
            button.classList.add('btn-info');
            button.classList.remove('btn-warning');
            viewDiffBtn.style.display = 'none';
        }
    }

    function handleCompareEnvVersions() {
        if (selectedVersionsForCompare.length === 2) {
            customAlert(`对比版本: ${selectedVersionsForCompare[0]} 和 ${selectedVersionsForCompare[1]}. (配置差异对比功能待实现)`);
        } else {
            customAlert("请选择两个版本进行对比。");
        }
    }

    function resetEnvVersionCompareMode() {
        envVersionCompareModeActive = false;
        selectedVersionsForCompare = [];
        const checkboxes = document.querySelectorAll('#environmentHistoryModal .env-version-checkbox');
        checkboxes.forEach(cb => {
            cb.style.display = 'none';
            cb.checked = false;
        });
        const compareBtn = document.getElementById('compareEnvVersionsBtn');
        if(compareBtn){
            compareBtn.innerHTML = '<svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16" style="margin-right:4px;"><path d="M0 5h16v1H0V5zm8 6h8v1H8v-1zM0 11h6v1H0v-1zM0 2h16v1H0V2zm8 3h8v1H8V5zM0 8h6v1H0V8z"/></svg>对比版本';
            compareBtn.classList.add('btn-info');
            compareBtn.classList.remove('btn-warning');
        }
        const viewDiffBtn = document.getElementById('viewVersionDiffBtn');
        if(viewDiffBtn) viewDiffBtn.style.display = 'none';
    }

    function toggleServiceSourceSection() {
        const codeRadio = document.getElementById('sourceCode');
        const importSection = document.getElementById('importFromCodeSection');

        if (codeRadio && importSection) {
            importSection.style.display = codeRadio.checked ? 'block' : 'none';
        }
    }

    function handleSaveService() {
        const serviceName = document.getElementById('serviceName').value;
        const serviceContent = document.getElementById('serviceContent').value;
        const editMode = document.getElementById('serviceEditMode').value;
        const sourceType = document.querySelector('input[name="serviceSource"]:checked').value;

        if (!serviceName.trim()) {
            customAlert('请输入服务名称！');
            return;
        }

        if (!serviceContent.trim()) {
            customAlert('请输入服务配置内容！');
            return;
        }

        const action = editMode === 'edit' ? '更新' : '添加';
        const sourceText = sourceType === 'code' ? '代码源' : '手动输入';

        customAlert(`${action}服务 "${serviceName}" (来源: ${sourceText}). (功能待实现)`);
        closeModal('serviceModal');
    }

    function handleImportFromCode() {
        const codeSource = document.getElementById('importCodeSource').value;
        const orgUser = document.getElementById('importOrgUser').value;
        const repository = document.getElementById('importRepository').value;
        const branch = document.getElementById('importBranch').value;
        const filePath = document.getElementById('importFilePath').value;

        if (!codeSource || !orgUser || !repository || !branch) {
            customAlert('请填写所有必填字段！');
            return;
        }

        // 模拟从代码源导入的内容
        const mockContent = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${repository}
  labels:
    app: ${repository}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ${repository}
  template:
    metadata:
      labels:
        app: ${repository}
    spec:
      containers:
      - name: ${repository}
        image: ${repository}:latest
        ports:
        - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: ${repository}-service
spec:
  selector:
    app: ${repository}
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP`;

        document.getElementById('serviceContent').value = mockContent;
        customAlert(`已从 ${codeSource}/${orgUser}/${repository}/${branch} 导入配置文件！`);
        closeModal('importFromCodeModal');
    }



    function addSelectedServices() {
        const checkboxes = document.querySelectorAll('input[name="deployServices"]:checked');
        const container = document.getElementById('selectedServicesContainer');

        checkboxes.forEach(checkbox => {
            const value = checkbox.value;
            const text = checkbox.value; // 服务名称就是值

            // 检查是否已经添加过这个服务
            if (!container.querySelector(`input[value="${value}"]`)) {
                addServiceToList(value, text, container);
            }

            // 取消选中复选框
            checkbox.checked = false;
        });

        // 显示/隐藏服务列表容器
        updateServiceListVisibility();
    }

    function addServiceToList(value, text, container) {
        const serviceItem = document.createElement('label');
        serviceItem.className = 'service-item';
        serviceItem.innerHTML = `
            <input type="checkbox" name="selectedServices" value="${value}" checked disabled>
            ${text}
            <button type="button" onclick="removeServiceFromList(this)" style="margin-left: 10px; padding: 2px 6px; border: none; background: #dc3545; color: white; border-radius: 3px; font-size: 12px;">×</button>
        `;
        container.appendChild(serviceItem);
    }

    function removeServiceFromList(button) {
        button.parentElement.remove();
        // 更新服务列表容器的显示状态
        updateServiceListVisibility();
    }

    function updateServiceListVisibility() {
        const container = document.getElementById('selectedDeployServicesList');
        const servicesContainer = document.getElementById('selectedServicesContainer');
        const hasServices = servicesContainer.children.length > 0;
        container.style.display = hasServices ? 'block' : 'none';
    }

    function handleDeployToEnvironment() {
        const envName = document.getElementById('deployEnvName').textContent;
        const selectedServices = Array.from(document.querySelectorAll('#selectedServicesContainer input[name="selectedServices"]'))
            .map(cb => cb.value);

        if (selectedServices.length === 0) {
            customAlert('请选择要发布的服务！');
            return;
        }

        customAlert(`将向环境 "${envName}" 发布服务: ${selectedServices.join(', ')}. (功能待实现)`);
        closeModal('deployToEnvironmentModal');
    }


    document.addEventListener('DOMContentLoaded', (event) => {
        const serviceManagementButton = document.querySelector(".tab-button[onclick*='serviceManagement']");
        if (serviceManagementButton) {
            openTab({ currentTarget: serviceManagementButton }, 'serviceManagement');
        } else {
            const firstMainTabButton = document.querySelector('.tab-navigation .tab-button');
            if (firstMainTabButton) {
                const onclickAttr = firstMainTabButton.getAttribute('onclick');
                if (onclickAttr) {
                    const match = onclickAttr.match(/openTab\s*\(\s*event\s*,\s*'([^']+)'\s*\)/);
                    if (match && match[1]) {
                        openTab({ currentTarget: firstMainTabButton }, match[1]);
                    }
                }
            }
        }

        const firstServiceSubTabButton = document.querySelector("#serviceManagement .sub-tab-navigation .sub-tab-button[onclick*='serviceReleaseView']");
        if(firstServiceSubTabButton){
            openServiceSubTab({currentTarget: firstServiceSubTabButton}, 'serviceReleaseView');
        }

        const firstCompileBuildSubTabButton = document.querySelector("#compileBuild .sub-tab-navigation .sub-tab-button[onclick*='buildTemplatesView']");
        if(firstCompileBuildSubTabButton){
            openCompileBuildSubTab({currentTarget: firstCompileBuildSubTabButton}, 'buildTemplatesView');
        }

        const firstProjectSettingsSubTabButton = document.querySelector("#projectSettings .sub-tab-navigation .sub-tab-button[onclick*='projectBasicInfo']");
        if(firstProjectSettingsSubTabButton){
            openProjectSettingsSubTab({currentTarget: firstProjectSettingsSubTabButton}, 'projectBasicInfo');
        }

        const versionCheckboxes = document.querySelectorAll('#environmentHistoryModal .env-version-checkbox');
        versionCheckboxes.forEach(cb => {
            cb.addEventListener('change', function() {
                if (this.checked) {
                    if (selectedVersionsForCompare.length < 2) {
                        selectedVersionsForCompare.push(this.value);
                    } else {
                        const firstSelectedValue = selectedVersionsForCompare.shift();
                        document.querySelector(`.env-version-checkbox[value="${firstSelectedValue}"]`).checked = false;
                        selectedVersionsForCompare.push(this.value);
                    }
                } else {
                    selectedVersionsForCompare = selectedVersionsForCompare.filter(v => v !== this.value);
                }

                const viewDiffBtn = document.getElementById('viewVersionDiffBtn');
                if (selectedVersionsForCompare.length === 2) {
                    viewDiffBtn.style.display = 'inline-block';
                } else {
                    viewDiffBtn.style.display = 'none';
                }
            });
        });

        // Service source radio button event listeners
        const serviceSourceRadios = document.querySelectorAll('input[name="serviceSource"]');
        serviceSourceRadios.forEach(radio => {
            radio.addEventListener('change', toggleServiceSourceSection);
        });

    });

    window.addEventListener('message', function(event) {
        // 只允许本地同源通信
        if (event.origin !== window.location.origin) return;
        if (event.data && event.data.type === 'openPreviewChangesModal') {
            openModal('previewChangesModal');
        }
    });

    function openServiceSubTab(evt, subTabName) {
        var i, subTabContent, subTabLinks;
        var parentTabPane = document.getElementById('serviceManagement');
        subTabContent = parentTabPane.getElementsByClassName("sub-tab-pane");
        for (i = 0; i < subTabContent.length; i++) {
            subTabContent[i].style.display = "none";
            subTabContent[i].classList.remove("active");
        }
        subTabLinks = parentTabPane.getElementsByClassName("sub-tab-button");
        for (i = 0; i < subTabLinks.length; i++) {
            subTabLinks[i].classList.remove("active");
        }
        const activeSubTabContent = document.getElementById(subTabName);
        if (activeSubTabContent) {
            activeSubTabContent.style.display = "block";
            activeSubTabContent.classList.add("active");
        }
        if (evt && evt.currentTarget) {
            evt.currentTarget.classList.add("active");
        }
    }

    // 版本对比相关函数
    function toggleAllReleases(checkbox) {
        const checkboxes = document.querySelectorAll('.release-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = checkbox.checked;
        });
        updateCompareButton();
    }

    function updateCompareButton() {
        const checkboxes = document.querySelectorAll('.release-checkbox:checked');
        const count = checkboxes.length;
        const compareBtn = document.getElementById('compareVersionsBtn');
        const countSpan = document.getElementById('selectedCount');

        countSpan.textContent = count;

        if (count === 2) {
            // 检查是否为相同服务和环境
            const selected = Array.from(checkboxes);
            const service1 = selected[0].dataset.service;
            const environment1 = selected[0].dataset.environment;
            const service2 = selected[1].dataset.service;
            const environment2 = selected[1].dataset.environment;

            if (service1 === service2 && environment1 === environment2) {
                compareBtn.disabled = false;
                compareBtn.title = `对比 ${service1} 在 ${environment1} 的两个版本`;
            } else {
                compareBtn.disabled = true;
                compareBtn.title = '只能对比相同服务和环境的版本';
            }
        } else {
            compareBtn.disabled = true;
            if (count === 0) {
                compareBtn.title = '请选择要对比的版本';
            } else if (count === 1) {
                compareBtn.title = '请再选择一个版本进行对比';
            } else {
                compareBtn.title = '只能选择两个版本进行对比';
            }
        }
    }

    function compareSelectedVersions() {
        const checkboxes = document.querySelectorAll('.release-checkbox:checked');
        if (checkboxes.length === 2) {
            const version1 = checkboxes[0].dataset.version;
            const version2 = checkboxes[1].dataset.version;
            const service = checkboxes[0].dataset.service;
            const environment = checkboxes[0].dataset.environment;

            customAlert(`版本对比功能：\n服务: ${service}\n环境: ${environment}\n版本1: ${version1}\n版本2: ${version2}\n\n此功能将显示两个版本之间的详细差异对比。`);
        }
    }

    // 发布详情模态框相关函数
    function openReleaseDetailModal(service, version, environment) {
        const modal = document.getElementById('releaseDetailModal');
        const title = document.getElementById('releaseDetailTitle');

        title.textContent = `${service} ${version} (${environment})`;

        // 重置到YAML标签页
        switchReleaseDetailTab(null, 'yamlTab');

        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    function switchReleaseDetailTab(evt, tabName) {
        // 隐藏所有标签页内容
        const tabContents = document.querySelectorAll('.release-detail-tab-content');
        tabContents.forEach(content => {
            content.style.display = 'none';
            content.classList.remove('active');
        });

        // 移除所有标签按钮的活动状态
        const tabButtons = document.querySelectorAll('.release-detail-tab');
        tabButtons.forEach(button => {
            button.classList.remove('active');
            button.style.borderBottomColor = 'transparent';
            button.style.color = '#4a5568';
        });

        // 显示选中的标签页内容
        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.style.display = 'block';
            activeContent.classList.add('active');
        }

        // 激活选中的标签按钮
        if (evt && evt.currentTarget) {
            evt.currentTarget.classList.add('active');
            evt.currentTarget.style.borderBottomColor = '#3182ce';
            evt.currentTarget.style.color = '#3182ce';
        } else {
            // 如果没有事件对象，手动激活对应的标签
            const targetButton = tabName === 'yamlTab' ?
                document.querySelector('.release-detail-tab:first-child') :
                document.querySelector('.release-detail-tab:last-child');
            if (targetButton) {
                targetButton.classList.add('active');
                targetButton.style.borderBottomColor = '#3182ce';
                targetButton.style.color = '#3182ce';
            }
        }
    }

    function copyYamlContent() {
        const yamlContent = document.getElementById('yamlContent');
        const text = yamlContent.textContent;

        navigator.clipboard.writeText(text).then(() => {
            customAlert('YAML内容已复制到剪贴板');
        }).catch(() => {
            customAlert('复制失败，请手动选择并复制');
        });
    }
</script>
</body>
</html>
